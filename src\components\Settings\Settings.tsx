import React, { useState, useEffect } from 'react';
import { 
  Settings as SettingsIcon, 
  Monitor, 
  Database, 
  RefreshCw, 
  Info,
  Moon,
  Sun,
  Globe,
  Bell,
  Shield
} from 'lucide-react';
import { useAppStore } from '../../stores/appStore';
import { 
  getAppInfo, 
  getPerformanceMetrics, 
  forceDataSync, 
  getLeagueList,
  setCurrentLeague 
} from '../../utils/tauri';
import type { AppInfo, PerformanceMetrics, League } from '../../types';
import LoadingSpinner from '../UI/LoadingSpinner';

const Settings: React.FC = () => {
  const [appInfo, setAppInfo] = useState<AppInfo | null>(null);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);
  const [leagues, setLeagues] = useState<League[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const { 
    theme, 
    setTheme, 
    currentLeague, 
    setCurrentLeague: setStoreCurrentLeague 
  } = useAppStore();

  useEffect(() => {
    loadSettingsData();
  }, []);

  const loadSettingsData = async () => {
    try {
      setLoading(true);
      
      const [appData, perfData, leagueData] = await Promise.all([
        getAppInfo(),
        getPerformanceMetrics(),
        getLeagueList(),
      ]);
      
      setAppInfo(appData);
      setPerformanceMetrics(perfData);
      setLeagues(leagueData);
    } catch (error) {
      console.error('Failed to load settings data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleForceSync = async () => {
    try {
      setSyncing(true);
      await forceDataSync();
      // Refresh performance metrics after sync
      const perfData = await getPerformanceMetrics();
      setPerformanceMetrics(perfData);
    } catch (error) {
      console.error('Force sync failed:', error);
      alert('Sync failed. Please try again.');
    } finally {
      setSyncing(false);
    }
  };

  const handleLeagueChange = async (league: string) => {
    try {
      await setCurrentLeague(league);
      setStoreCurrentLeague(league);
    } catch (error) {
      console.error('Failed to set league:', error);
    }
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatMemory = (mb: number) => {
    if (mb >= 1024) {
      return `${(mb / 1024).toFixed(1)} GB`;
    }
    return `${mb.toFixed(0)} MB`;
  };

  if (loading) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="text-center py-12">
          <LoadingSpinner size="large" className="mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          Settings
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Configure your PoE Profit AI experience
        </p>
      </div>

      {/* General Settings */}
      <div className="card p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center text-gray-900 dark:text-gray-100">
          <SettingsIcon className="w-5 h-5 mr-2" />
          General Settings
        </h2>
        
        <div className="space-y-6">
          {/* Theme */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {theme === 'dark' ? (
                <Moon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              ) : (
                <Sun className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              )}
              <div>
                <p className="font-medium text-gray-900 dark:text-gray-100">Theme</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Choose your preferred color scheme
                </p>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={() => setTheme('light')}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  theme === 'light'
                    ? 'bg-poe-accent text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                Light
              </button>
              <button
                onClick={() => setTheme('dark')}
                className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  theme === 'dark'
                    ? 'bg-poe-accent text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                Dark
              </button>
            </div>
          </div>

          {/* Default League */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Globe className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              <div>
                <p className="font-medium text-gray-900 dark:text-gray-100">Default League</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Your primary league for price lookups
                </p>
              </div>
            </div>
            
            <select
              value={currentLeague}
              onChange={(e) => handleLeagueChange(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              {leagues.map((league) => (
                <option key={league.id} value={league.name}>
                  {league.display_name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Data & Sync */}
      <div className="card p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center text-gray-900 dark:text-gray-100">
          <Database className="w-5 h-5 mr-2" />
          Data & Synchronization
        </h2>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900 dark:text-gray-100">Force Data Sync</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Manually trigger a data synchronization from poe.ninja
              </p>
            </div>
            
            <button
              onClick={handleForceSync}
              disabled={syncing}
              className="btn-primary flex items-center"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${syncing ? 'animate-spin' : ''}`} />
              {syncing ? 'Syncing...' : 'Sync Now'}
            </button>
          </div>
          
          {appInfo && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-600">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Database Size</p>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  {formatMemory(appInfo.database_size_mb)}
                </p>
              </div>
              
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Cache Size</p>
                <p className="font-medium text-gray-900 dark:text-gray-100">
                  {appInfo.cache_size} items
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Performance */}
      {performanceMetrics && (
        <div className="card p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center text-gray-900 dark:text-gray-100">
            <Monitor className="w-5 h-5 mr-2" />
            Performance Metrics
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Query Time</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {performanceMetrics.avg_query_time_ms.toFixed(0)}ms
              </p>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Cache Hit Rate</p>
              <p className="text-2xl font-bold text-green-600">
                {(performanceMetrics.cache_hit_rate * 100).toFixed(0)}%
              </p>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Memory Usage</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatMemory(performanceMetrics.memory_usage_mb)}
              </p>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">CPU Usage</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {performanceMetrics.cpu_usage_percent.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      )}

      {/* App Information */}
      {appInfo && (
        <div className="card p-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center text-gray-900 dark:text-gray-100">
            <Info className="w-5 h-5 mr-2" />
            Application Information
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Version</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">
                v{appInfo.version}
              </p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Uptime</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">
                {formatUptime(appInfo.uptime_seconds)}
              </p>
            </div>
            
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Startup Time</p>
              <p className="font-medium text-gray-900 dark:text-gray-100">
                {appInfo.startup_time_ms}ms
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Privacy & Security */}
      <div className="card p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center text-gray-900 dark:text-gray-100">
          <Shield className="w-5 h-5 mr-2" />
          Privacy & Security
        </h2>
        
        <div className="space-y-4 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
            <div>
              <p className="font-medium text-gray-900 dark:text-gray-100">Local Processing</p>
              <p>All data processing happens locally on your device. No personal data is transmitted.</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
            <div>
              <p className="font-medium text-gray-900 dark:text-gray-100">GGG Compliant</p>
              <p>No game client interaction, memory reading, or automation. Fully compliant with Path of Exile terms of service.</p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
            <div>
              <p className="font-medium text-gray-900 dark:text-gray-100">Secure Data Sources</p>
              <p>Data sourced exclusively from public APIs (poe.ninja) with rate limiting and error handling.</p>
            </div>
          </div>
        </div>
      </div>

      {/* About */}
      <div className="card p-6 text-center">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          PoE Profit AI
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Lightning-fast Path of Exile market analysis companion
        </p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Built with ❤️ for the Path of Exile community
        </p>
      </div>
    </div>
  );
};

export default Settings;
