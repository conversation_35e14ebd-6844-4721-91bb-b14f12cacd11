import React, { useState, useEffect } from 'react';
import { FlaskConical, TrendingUp, Calendar, AlertTriangle, Lightbulb } from 'lucide-react';
import { useAppStore } from '../../stores/appStore';
import { getLeagueList, getMarketSummary } from '../../utils/tauri';
import type { League, MarketSummary } from '../../types';
import LoadingSpinner from '../UI/LoadingSpinner';

const LeagueLab: React.FC = () => {
  const [leagues, setLeagues] = useState<League[]>([]);
  const [selectedLeague, setSelectedLeague] = useState<string>('');
  const [marketData, setMarketData] = useState<MarketSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | undefined>();
  const { currentLeague } = useAppStore();

  useEffect(() => {
    loadLeagueData();
  }, []);

  useEffect(() => {
    if (selectedLeague) {
      loadMarketData(selectedLeague);
    }
  }, [selectedLeague]);

  const loadLeagueData = async () => {
    try {
      setLoading(true);
      const leagueList = await getLeagueList();
      setLeagues(leagueList);
      
      // Set current league as default
      if (leagueList.length > 0) {
        const current = leagueList.find(l => l.name === currentLeague) || leagueList[0];
        setSelectedLeague(current.name);
      }
    } catch (err) {
      console.error('Failed to load leagues:', err);
      setError('Failed to load league data');
    } finally {
      setLoading(false);
    }
  };

  const loadMarketData = async (league: string) => {
    try {
      const data = await getMarketSummary(league);
      setMarketData(data);
    } catch (err) {
      console.error('Failed to load market data:', err);
    }
  };

  // Mock data for league analysis features
  const leagueInsights = [
    {
      title: "Early League Opportunities",
      description: "Items that typically spike in value during the first week",
      items: ["Tabula Rasa", "Goldrim", "Wanderlust", "Lifesprig"],
      icon: <TrendingUp className="w-5 h-5 text-green-500" />,
      type: "opportunity"
    },
    {
      title: "Currency Farming Focus",
      description: "Most efficient currency farming methods for this league",
      items: ["Heist", "Delve", "Essence", "Harbinger"],
      icon: <Lightbulb className="w-5 h-5 text-yellow-500" />,
      type: "strategy"
    },
    {
      title: "Market Volatility Warning",
      description: "Items with high price volatility - trade with caution",
      items: ["Unique Jewels", "Influenced Bases", "Synthesised Items"],
      icon: <AlertTriangle className="w-5 h-5 text-red-500" />,
      type: "warning"
    }
  ];

  const upcomingEvents = [
    {
      title: "League End Approaching",
      date: "2025-07-15",
      description: "Standard merge expected - consider liquidating league-specific items",
      type: "warning"
    },
    {
      title: "Patch 3.26 Release",
      date: "2025-07-01",
      description: "Major balance changes expected - monitor meta shifts",
      type: "info"
    }
  ];

  if (loading) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <div className="text-center py-12">
          <LoadingSpinner size="large" className="mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading league data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          League Laboratory
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Advanced market analysis and league-specific insights
        </p>
      </div>

      {/* League Selector */}
      <div className="card p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center text-gray-900 dark:text-gray-100">
          <FlaskConical className="w-5 h-5 mr-2" />
          Select League for Analysis
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {leagues.map((league) => (
            <button
              key={league.id}
              onClick={() => setSelectedLeague(league.name)}
              className={`p-4 rounded-lg border-2 transition-all ${
                selectedLeague === league.name
                  ? 'border-poe-accent bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
              }`}
            >
              <div className="text-left">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                  {league.display_name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {league.is_active ? 'Active' : 'Inactive'}
                </p>
                {league.name === currentLeague && (
                  <span className="inline-block mt-1 px-2 py-1 bg-poe-accent text-white text-xs rounded">
                    Current
                  </span>
                )}
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Market Overview */}
      {marketData && (
        <div className="card p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
            Market Overview - {selectedLeague}
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Total Items</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {marketData.total_items.toLocaleString()}
              </p>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Chaos:Divine</p>
              <p className="text-2xl font-bold divine-value">
                1:{marketData.currency_rates.divine_to_chaos.toFixed(0)}
              </p>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Market Activity</p>
              <p className="text-2xl font-bold text-green-600">
                High
              </p>
            </div>
            
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">Volatility</p>
              <p className="text-2xl font-bold text-yellow-600">
                Medium
              </p>
            </div>
          </div>
        </div>
      )}

      {/* League Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {leagueInsights.map((insight, index) => (
          <div key={index} className="card p-6">
            <div className="flex items-center mb-3">
              {insight.icon}
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 ml-2">
                {insight.title}
              </h3>
            </div>
            
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              {insight.description}
            </p>
            
            <div className="space-y-2">
              {insight.items.map((item, itemIndex) => (
                <div
                  key={itemIndex}
                  className="px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded text-sm text-gray-900 dark:text-gray-100"
                >
                  {item}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Upcoming Events */}
      <div className="card p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center text-gray-900 dark:text-gray-100">
          <Calendar className="w-5 h-5 mr-2" />
          Upcoming Events & Deadlines
        </h2>
        
        <div className="space-y-4">
          {upcomingEvents.map((event, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border-l-4 ${
                event.type === 'warning'
                  ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                  : 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">
                    {event.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {event.description}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {new Date(event.date).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Coming Soon Features */}
      <div className="card p-6 text-center">
        <FlaskConical className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
          More Features Coming Soon
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          We're working on advanced league analysis features including:
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-500 dark:text-gray-400">
          <div>• Historical league comparison</div>
          <div>• Meta shift predictions</div>
          <div>• Patch note impact analysis</div>
          <div>• League mechanic profitability</div>
          <div>• Early league investment strategies</div>
          <div>• End-league liquidation planning</div>
        </div>
      </div>
    </div>
  );
};

export default LeagueLab;
