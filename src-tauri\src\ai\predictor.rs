use std::collections::HashMap;
use anyhow::Result;
use chrono::{DateTime, Utc, Duration, Timelike, Datelike};
use ndarray::{Array1, Array2};
use linfa::prelude::*;
use linfa_linear::LinearRegression;
use statrs::statistics::{Statistics, OrderStatistics};

use crate::data::DatabaseManager;
use crate::models::Price;
use super::models::*;

pub struct PricePredictor {
    models: HashMap<String, LinearRegression<f64>>,
    feature_scalers: HashMap<String, FeatureScaler>,
    model_metrics: HashMap<String, ModelMetrics>,
}

#[derive(Debug, Clone)]
struct FeatureScaler {
    mean: Array1<f64>,
    std: Array1<f64>,
}

impl FeatureScaler {
    fn new(data: &Array2<f64>) -> Self {
        let mean = data.mean_axis(ndarray::Axis(0)).unwrap();
        let std = data.std_axis(ndarray::Axis(0), 0.0);
        Self { mean, std }
    }
    
    fn transform(&self, data: &Array2<f64>) -> Array2<f64> {
        (data - &self.mean) / &self.std
    }
    
    fn transform_single(&self, data: &Array1<f64>) -> Array1<f64> {
        (data - &self.mean) / &self.std
    }
}

impl PricePredictor {
    pub fn new() -> Self {
        Self {
            models: HashMap::new(),
            feature_scalers: HashMap::new(),
            model_metrics: HashMap::new(),
        }
    }
    
    pub async fn train_models(&mut self, db: &DatabaseManager) -> Result<()> {
        log::info!("Starting ML model training...");
        
        // Get training data for different item categories
        let categories = vec!["Currency", "UniqueWeapon", "UniqueArmour", "DivinationCard"];
        
        for category in categories {
            match self.train_category_model(db, category).await {
                Ok(metrics) => {
                    log::info!("Trained model for {}: accuracy={:.3}", category, metrics.accuracy);
                    self.model_metrics.insert(category.to_string(), metrics);
                }
                Err(e) => {
                    log::warn!("Failed to train model for {}: {}", category, e);
                }
            }
        }
        
        log::info!("ML model training completed");
        Ok(())
    }
    
    async fn train_category_model(&mut self, db: &DatabaseManager, category: &str) -> Result<ModelMetrics> {
        // Get historical price data for this category
        let training_data = self.prepare_training_data(db, category).await?;
        
        if training_data.len() < 100 {
            return Err(anyhow::anyhow!("Insufficient training data for {}", category));
        }
        
        // Extract features and targets
        let (features, targets) = self.extract_features_and_targets(&training_data)?;
        
        // Create feature scaler
        let scaler = FeatureScaler::new(&features);
        let scaled_features = scaler.transform(&features);
        
        // Split into train/test
        let split_idx = (scaled_features.nrows() as f64 * 0.8) as usize;
        let (train_features, test_features) = scaled_features.view().split_at(ndarray::Axis(0), split_idx);
        let (train_targets, test_targets) = targets.view().split_at(ndarray::Axis(0), split_idx);
        
        // Train linear regression model
        let dataset = Dataset::new(train_features.to_owned(), train_targets.to_owned());
        let model = LinearRegression::default().fit(&dataset)?;
        
        // Evaluate model
        let predictions = model.predict(&test_features.to_owned());
        let metrics = self.calculate_metrics(&test_targets.to_owned(), &predictions);
        
        // Store model and scaler
        self.models.insert(category.to_string(), model);
        self.feature_scalers.insert(category.to_string(), scaler);
        
        Ok(ModelMetrics {
            model_name: category.to_string(),
            accuracy: metrics.0,
            precision: metrics.1,
            recall: metrics.2,
            f1_score: metrics.3,
            mean_absolute_error: metrics.4,
            root_mean_square_error: metrics.5,
            training_samples: training_data.len(),
            last_trained: Utc::now(),
        })
    }
    
    async fn prepare_training_data(&self, db: &DatabaseManager, category: &str) -> Result<Vec<(MarketFeatures, f64)>> {
        // This would query the database for historical price data
        // For now, we'll create a simplified version
        
        let mut training_data = Vec::new();
        
        // Get items in this category
        let items = db.get_items_by_category(category).await?;
        
        for item in items.iter().take(50) { // Limit for demo
            // Get price history for this item
            let prices = db.get_price_history(item.id, 30).await?;
            
            if prices.len() < 10 {
                continue;
            }
            
            // Create training samples from price history
            for window in prices.windows(8) {
                if window.len() < 8 {
                    continue;
                }
                
                let features = self.extract_market_features(&window[..7], item.id).await?;
                let target = window[7].chaos_value.unwrap_or(0.0);
                
                if target > 0.0 {
                    training_data.push((features, target));
                }
            }
        }
        
        Ok(training_data)
    }
    
    async fn extract_market_features(&self, price_window: &[Price], item_id: i64) -> Result<MarketFeatures> {
        let current_price = price_window.last().unwrap().chaos_value.unwrap_or(0.0);
        let prices: Vec<f64> = price_window.iter()
            .filter_map(|p| p.chaos_value)
            .collect();
        
        if prices.is_empty() {
            return Err(anyhow::anyhow!("No valid prices in window"));
        }
        
        let price_7d_avg = prices.iter().sum::<f64>() / prices.len() as f64;
        let price_24h_change = if prices.len() >= 2 {
            ((current_price - prices[prices.len() - 2]) / prices[prices.len() - 2]) * 100.0
        } else {
            0.0
        };
        
        let price_7d_change = if prices.len() >= 7 {
            ((current_price - prices[0]) / prices[0]) * 100.0
        } else {
            0.0
        };
        
        // Calculate volatility (standard deviation)
        let volatility_7d = if prices.len() > 1 {
            prices.std_dev()
        } else {
            0.0
        };
        
        let now = Utc::now();
        let listing_count = price_window.last().unwrap().listing_count;
        
        Ok(MarketFeatures {
            item_id,
            current_price,
            price_7d_avg,
            price_24h_change,
            price_7d_change,
            volatility_7d,
            listing_count,
            listing_count_change_24h: 0.0, // Simplified
            volume_7d: current_price * listing_count as f64, // Approximation
            market_cap_rank: None,
            league_age_days: 30.0, // Simplified
            is_weekend: now.weekday().number_from_monday() >= 6,
            hour_of_day: now.hour() as u8,
            day_of_week: now.weekday().number_from_monday() as u8,
        })
    }
    
    fn extract_features_and_targets(&self, data: &[(MarketFeatures, f64)]) -> Result<(Array2<f64>, Array1<f64>)> {
        let n_samples = data.len();
        let n_features = 12; // Number of features we'll use
        
        let mut features = Array2::zeros((n_samples, n_features));
        let mut targets = Array1::zeros(n_samples);
        
        for (i, (market_features, target)) in data.iter().enumerate() {
            features[[i, 0]] = market_features.current_price;
            features[[i, 1]] = market_features.price_7d_avg;
            features[[i, 2]] = market_features.price_24h_change;
            features[[i, 3]] = market_features.price_7d_change;
            features[[i, 4]] = market_features.volatility_7d;
            features[[i, 5]] = market_features.listing_count as f64;
            features[[i, 6]] = market_features.volume_7d;
            features[[i, 7]] = market_features.league_age_days;
            features[[i, 8]] = if market_features.is_weekend { 1.0 } else { 0.0 };
            features[[i, 9]] = market_features.hour_of_day as f64;
            features[[i, 10]] = market_features.day_of_week as f64;
            features[[i, 11]] = market_features.listing_count_change_24h;
            
            targets[i] = *target;
        }
        
        Ok((features, targets))
    }
    
    fn calculate_metrics(&self, actual: &Array1<f64>, predicted: &Array1<f64>) -> (f64, f64, f64, f64, f64, f64) {
        let n = actual.len() as f64;
        
        // Mean Absolute Error
        let mae = actual.iter().zip(predicted.iter())
            .map(|(a, p)| (a - p).abs())
            .sum::<f64>() / n;
        
        // Root Mean Square Error
        let rmse = (actual.iter().zip(predicted.iter())
            .map(|(a, p)| (a - p).powi(2))
            .sum::<f64>() / n).sqrt();
        
        // R² Score (coefficient of determination)
        let actual_mean = actual.mean().unwrap();
        let ss_tot = actual.iter().map(|a| (a - actual_mean).powi(2)).sum::<f64>();
        let ss_res = actual.iter().zip(predicted.iter())
            .map(|(a, p)| (a - p).powi(2))
            .sum::<f64>();
        
        let r2 = 1.0 - (ss_res / ss_tot);
        
        // For regression, we'll use R² as accuracy and simplified precision/recall
        (r2.max(0.0), r2.max(0.0), r2.max(0.0), r2.max(0.0), mae, rmse)
    }
    
    pub async fn predict_price(&self, db: &DatabaseManager, item_id: i64, category: &str, hours_ahead: u32) -> Result<PricePrediction> {
        let model = self.models.get(category)
            .ok_or_else(|| anyhow::anyhow!("No model trained for category: {}", category))?;
        
        let scaler = self.feature_scalers.get(category)
            .ok_or_else(|| anyhow::anyhow!("No scaler found for category: {}", category))?;
        
        // Get recent price data for features
        let recent_prices = db.get_price_history(item_id, 7).await?;
        if recent_prices.is_empty() {
            return Err(anyhow::anyhow!("No recent price data for item {}", item_id));
        }
        
        // Extract features
        let features = self.extract_market_features(&recent_prices, item_id).await?;
        let feature_array = self.features_to_array(&features);
        let scaled_features = scaler.transform_single(&feature_array);
        
        // Make prediction
        let prediction = model.predict(&scaled_features.insert_axis(ndarray::Axis(0)));
        let predicted_price = prediction[0].max(0.0);
        
        // Calculate confidence based on model metrics and data quality
        let model_metrics = self.model_metrics.get(category);
        let base_confidence = model_metrics.map(|m| m.accuracy).unwrap_or(0.5);
        
        // Adjust confidence based on data freshness and volatility
        let volatility_penalty = (features.volatility_7d / features.current_price).min(0.5);
        let confidence = (base_confidence - volatility_penalty).max(0.1).min(0.95);
        
        Ok(PricePrediction {
            item_id,
            predicted_price,
            confidence,
            prediction_horizon_hours: hours_ahead,
            model_version: "linear_v1".to_string(),
            features_used: vec![
                "current_price".to_string(),
                "price_7d_avg".to_string(),
                "volatility_7d".to_string(),
                "listing_count".to_string(),
            ],
            created_at: Utc::now(),
        })
    }
    
    fn features_to_array(&self, features: &MarketFeatures) -> Array1<f64> {
        Array1::from(vec![
            features.current_price,
            features.price_7d_avg,
            features.price_24h_change,
            features.price_7d_change,
            features.volatility_7d,
            features.listing_count as f64,
            features.volume_7d,
            features.league_age_days,
            if features.is_weekend { 1.0 } else { 0.0 },
            features.hour_of_day as f64,
            features.day_of_week as f64,
            features.listing_count_change_24h,
        ])
    }
    
    pub fn get_model_metrics(&self, category: &str) -> Option<&ModelMetrics> {
        self.model_metrics.get(category)
    }
}
