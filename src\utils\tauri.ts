import { invoke } from '@tauri-apps/api/tauri';
import type {
  PriceQuery,
  PriceResult,
  FlipFinderQuery,
  FlipOpportunity,
  PortfolioItem,
  MarketSummary,
  League,
  AppInfo,
  PerformanceMetrics,
  SyncStatus,
} from '../types';

// Price commands
export const quickPriceLookup = async (query: PriceQuery): Promise<PriceResult> => {
  return invoke('quick_price_lookup', { query });
};

export const searchItemNames = async (query: string, limit: number = 5): Promise<string[]> => {
  return invoke('search_item_names', { query, limit });
};

export const getPriceHistory = async (
  itemName: string,
  league: string,
  days: number
): Promise<any[]> => {
  return invoke('get_price_history', { itemName, league, days });
};

// Flip finder commands
export const findFlipOpportunities = async (query: FlipFinderQuery): Promise<FlipOpportunity[]> => {
  return invoke('find_flip_opportunities', { query });
};

export const getTopFlipOpportunities = async (
  league: string,
  limit: number = 10
): Promise<FlipOpportunity[]> => {
  return invoke('get_top_flip_opportunities', { league, limit });
};

// Portfolio commands
export const getPortfolioItems = async (league: string): Promise<PortfolioItem[]> => {
  return invoke('get_portfolio_items', { league });
};

export const addPortfolioItem = async (item: PortfolioItem): Promise<number> => {
  return invoke('add_portfolio_item', { item });
};

export const removePortfolioItem = async (id: number): Promise<void> => {
  return invoke('remove_portfolio_item', { id });
};

export const getPortfolioTotalValue = async (league: string): Promise<number> => {
  return invoke('get_portfolio_total_value', { league });
};

// Market commands
export const getMarketSummary = async (league: string): Promise<MarketSummary> => {
  return invoke('get_market_summary', { league });
};

export const getLeagueList = async (): Promise<League[]> => {
  return invoke('get_league_list');
};

export const setCurrentLeague = async (league: string): Promise<void> => {
  return invoke('set_current_league', { league });
};

// System commands
export const getAppInfo = async (): Promise<AppInfo> => {
  return invoke('get_app_info');
};

export const getPerformanceMetrics = async (): Promise<PerformanceMetrics> => {
  return invoke('get_performance_metrics');
};

export const forceDataSync = async (): Promise<string> => {
  return invoke('force_data_sync');
};

export const getSyncStatus = async (): Promise<SyncStatus> => {
  return invoke('get_sync_status');
};

// Utility functions
export const formatCurrency = (value: number | undefined | null, currency: 'chaos' | 'divine' = 'chaos'): string => {
  if (value === undefined || value === null) return 'N/A';
  
  const suffix = currency === 'chaos' ? 'c' : 'd';
  
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}k${suffix}`;
  } else if (value >= 100) {
    return `${value.toFixed(0)}${suffix}`;
  } else if (value >= 10) {
    return `${value.toFixed(1)}${suffix}`;
  } else {
    return `${value.toFixed(2)}${suffix}`;
  }
};

export const formatPercentage = (value: number | undefined | null): string => {
  if (value === undefined || value === null) return 'N/A';
  
  const sign = value >= 0 ? '+' : '';
  return `${sign}${value.toFixed(1)}%`;
};

export const formatTimeAgo = (timestamp: string): string => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffMs = now.getTime() - time.getTime();
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return time.toLocaleDateString();
};

export const getRiskLevelColor = (risk: string): string => {
  switch (risk.toLowerCase()) {
    case 'low': return 'text-green-600 dark:text-green-400';
    case 'medium': return 'text-yellow-600 dark:text-yellow-400';
    case 'high': return 'text-red-600 dark:text-red-400';
    default: return 'text-gray-600 dark:text-gray-400';
  }
};

export const getLiquidityColor = (liquidity: string): string => {
  switch (liquidity.toLowerCase()) {
    case 'very high':
    case 'high': return 'text-green-600 dark:text-green-400';
    case 'medium': return 'text-yellow-600 dark:text-yellow-400';
    case 'low': return 'text-red-600 dark:text-red-400';
    default: return 'text-gray-600 dark:text-gray-400';
  }
};
