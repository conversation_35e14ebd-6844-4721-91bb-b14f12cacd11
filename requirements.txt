# Core async framework
asyncio-mqtt==0.16.1
asyncpg==0.29.0
aiohttp==3.9.1
aiofiles==23.2.1

# Web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
starlette==0.27.0

# Database
asyncpg==0.29.0
sqlalchemy[asyncio]==2.0.23
alembic==1.13.1

# Data validation and processing
jsonschema==4.20.0
pydantic==2.5.2
pydantic-settings==2.1.0

# Data analysis
numpy==1.26.2
pandas==2.1.4
scipy==1.11.4

# HTTP client
httpx==0.25.2
requests==2.31.0

# Configuration and environment
python-dotenv==1.0.0
pyyaml==6.0.1

# Logging and monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Utilities
click==8.1.7
rich==13.7.0
typer==0.9.0

# Time handling
python-dateutil==2.8.2
pytz==2023.3

# JSON processing (for performance)
orjson==3.9.10

# Compression (for cold start snapshots)
zstandard==0.22.0

# CORS support
fastapi-cors==0.0.6

# Background tasks
celery==5.3.4
redis==5.0.1

# Health checks
psutil==5.9.6

# Rate limiting
slowapi==0.1.9
limits==3.6.0
