# PoE Dashboard - Data Acquisition & Sync System

A production-ready data acquisition system for Path of Exile economy data, implementing comprehensive sync blueprint with robust error handling, monitoring, and data quality guardrails.

## 🚀 Features

### Core Data Acquisition
- **31-minute polling cadence** with +1 min offset to avoid thundering herd
- **<PERSON><PERSON><PERSON> fetching** with max 6 concurrent requests and jittered start times
- **Exponential backoff retry** (0.5s, 1s, 2s) with comprehensive error handling
- **Automatic league discovery** from poe.ninja index state
- **Complete endpoint coverage** (31 endpoints: currency + 29 item types)

### Data Quality & Validation
- **Schema validation** with pre-compiled JSON schemas
- **Confidence filtering** (count ≥ 5, listingCount ≥ 10)
- **Currency sanity checks** (chaosEquivalent ∈ [0.5c, 100,000c])
- **Outlier detection** (σ > 4 statistical filtering)
- **Stale data detection** (>90 min threshold with UI warnings)

### Performance & Reliability
- **Respectful API etiquette** (45 req/min global, 5 req/min per endpoint)
- **Automatic backoff** on consecutive failures (60-min intervals)
- **Connection pooling** with async PostgreSQL operations
- **Materialized views** for fast query performance
- **Health monitoring** with comprehensive metrics

### Monitoring & Operations
- **Real-time health checks** (database, API, data quality, pipeline integrity)
- **Fetch metrics tracking** (latency, success rates, error rates)
- **Data quality metrics** (confidence scores, outlier rates, liquidity)
- **RESTful API** for monitoring and control
- **Grafana dashboards** (optional) for visualization

## 📋 Requirements

- Python 3.11+
- PostgreSQL 15+
- Redis 7+ (optional, for caching)
- Docker & Docker Compose (recommended)

## 🛠️ Quick Start

### Option 1: Docker Compose (Recommended)

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd poe-dashboard
   ```

2. **Start all services**:
   ```bash
   docker-compose up -d
   ```

3. **Check health**:
   ```bash
   curl http://localhost:8000/health
   ```

4. **View logs**:
   ```bash
   docker-compose logs -f app
   ```

### Option 2: Local Development

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Setup PostgreSQL**:
   ```bash
   # Create database
   createdb poe_dashboard
   
   # Run schema
   psql poe_dashboard < database/schema.sql
   ```

3. **Configure environment**:
   ```bash
   export DATABASE_URL="postgresql://postgres:password@localhost:5432/poe_dashboard"
   export POE_DEFAULT_LEAGUE="Mercenaries"
   export LOG_LEVEL="INFO"
   ```

4. **Run application**:
   ```bash
   python main.py
   ```

## 🔧 Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DATABASE_URL` | `postgresql://postgres:password@localhost:5432/poe_dashboard` | PostgreSQL connection string |
| `POE_DEFAULT_LEAGUE` | `Mercenaries` | Default PoE league to track |
| `SCHEDULER_POLL_INTERVAL` | `31` | Polling interval in minutes |
| `API_MAX_CONCURRENT` | `6` | Max concurrent API requests |
| `API_MAX_REQ_PER_MIN` | `45` | Global rate limit (requests/min) |
| `VALIDATION_MIN_LISTING_COUNT` | `10` | Minimum listing count for confidence |
| `LOG_LEVEL` | `INFO` | Logging level (DEBUG, INFO, WARNING, ERROR) |

### Blueprint Configuration

The system implements the comprehensive sync blueprint with these key settings:

- **Polling**: 31-min intervals with +1 min offset
- **Concurrency**: Max 6 parallel requests with ±5s jitter
- **Rate Limits**: 45 req/min global, 5 req/min per endpoint
- **Quality Filters**: count ≥ 5, listingCount ≥ 10, σ ≤ 4
- **Backoff**: 60-min intervals after 3 consecutive failures

## 📊 API Endpoints

### Health & Status
- `GET /health` - Comprehensive health check
- `GET /status` - Application status and metrics
- `GET /metrics` - Detailed performance metrics

### Data Access
- `GET /data/current-prices?league=<league>&limit=<limit>` - Current prices
- `GET /data/top-movers?league=<league>&limit=<limit>` - Price movers

### Control
- `POST /scheduler/pause` - Pause data acquisition
- `POST /scheduler/resume` - Resume data acquisition

## 🗄️ Database Schema

### Core Tables
- **`leagues`** - Active and historical PoE leagues
- **`items`** - Item catalog with stable cross-endpoint join keys
- **`prices`** - Historical price data with calculated metrics
- **`staging_raw`** - Raw JSON staging for ETL pipeline
- **`stats_fetch`** - API fetch metrics for monitoring

### Materialized Views
- **`v_current_price`** - Latest prices with quality filters
- **`v_top_movers`** - Items with significant price changes
- **`v_high_liquidity`** - High-liquidity items
- **`v_outliers`** - Statistical outliers (σ > 4)

## 📈 Monitoring

### Health Checks
The system performs comprehensive health monitoring:

1. **Database Health** - Connectivity, query performance, connection pool
2. **Data Freshness** - Stale data detection (>90 min threshold)
3. **Fetch Performance** - Success rates, latency, endpoint coverage
4. **Data Quality** - Confidence scores, outlier rates, liquidity
5. **Pipeline Integrity** - Processing errors, staging backlog

### Metrics Collection
- **Fetch Metrics**: Latency, success rates, error counts per endpoint
- **Data Metrics**: Record counts, confidence scores, outlier detection
- **System Metrics**: Database performance, connection pool status

### Alerting Thresholds
- **Critical**: Success rate < 99%, data age > 90 min, database down
- **Warning**: High latency > 120ms, low confidence < 70%, processing errors

## 🔍 Data Quality Guardrails

### Confidence Filters
- Discard rows where `count < 5` OR `listingCount < 10`
- Flag outliers where volatility σ > 4
- Currency sanity: `chaosEquivalent ∈ [0.5c, 100,000c]`

### Stale Data Handling
- Banner warning if no successful sync for >90 min
- Automatic fallback to last known good data
- UI indicators for data freshness

## 🚨 Error Handling

### Retry Logic
- **3x retry** with exponential backoff (0.5s, 1s, 2s)
- **Rate limit handling** with automatic delays
- **Circuit breaker** after 3 consecutive failures

### Graceful Degradation
- Continue with partial data on endpoint failures
- Maintain service availability during API issues
- Automatic recovery after backoff periods

## 📝 Logging

Structured logging with configurable levels:
- **INFO**: Normal operations, successful syncs
- **WARNING**: Retries, quality issues, performance concerns
- **ERROR**: Failed requests, processing errors
- **DEBUG**: Detailed request/response data

## 🔒 Security

- **Non-root container** execution
- **Environment-based** configuration
- **Connection pooling** with limits
- **Input validation** and sanitization
- **CORS configuration** for web access

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the health endpoint: `/health`
2. Review application logs
3. Verify database connectivity
4. Check API rate limits

## 🔄 Maintenance

### Regular Tasks
- Monitor health dashboard
- Review error logs
- Update league configurations
- Backup database regularly

### Performance Tuning
- Adjust polling intervals based on load
- Monitor connection pool usage
- Review materialized view refresh frequency
- Optimize database indexes as needed
