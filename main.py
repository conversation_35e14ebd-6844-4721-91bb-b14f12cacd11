"""
PoE Dashboard Main Application
=============================
Entry point for the comprehensive data acquisition and sync system
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from typing import Optional

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from config.settings import get_settings
# from ingestion.scheduler import DataAcquisitionScheduler, SchedulerState
# from database.connection import DatabaseConnection
# from monitoring.health_checks import HealthChecker

# Configure logging
def setup_logging():
    """Setup application logging"""
    settings = get_settings()
    
    logging.basicConfig(
        level=getattr(logging, settings.logging.level),
        format=settings.logging.format,
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(settings.get_log_file_path()) if settings.get_log_file_path() else logging.NullHandler()
        ]
    )
    
    # Reduce noise from external libraries
    logging.getLogger("asyncpg").setLevel(logging.WARNING)
    logging.getLogger("aiohttp").setLevel(logging.WARNING)
    logging.getLogger("uvicorn").setLevel(logging.INFO)

setup_logging()
logger = logging.getLogger(__name__)

# Global application state
app_state = {
    'scheduler': None,
    'db_connection': None,
    'health_checker': None,
    'startup_time': None,
    'shutdown_requested': False
}

# FastAPI application
app = FastAPI(
    title="PoE Dashboard API",
    description="Production-ready data acquisition system for Path of Exile economy data",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
settings = get_settings()
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.web.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Application startup"""
    logger.info("Starting PoE Dashboard application")
    app_state['startup_time'] = datetime.now()

    try:
        # For now, start without database to test basic functionality
        logger.info("Starting in basic mode (no database)")

        # TODO: Initialize database connection when available
        # app_state['db_connection'] = DatabaseConnection(settings.get_database_url())
        # await app_state['db_connection'].connect()

        # TODO: Initialize health checker when database is available
        # app_state['health_checker'] = HealthChecker(app_state['db_connection'])

        # TODO: Initialize and start scheduler when database is available
        # logger.info("Initializing data acquisition scheduler")
        # app_state['scheduler'] = DataAcquisitionScheduler(
        #     config=settings.scheduler,
        #     league=settings.api.default_league,
        #     database_url=settings.get_database_url()
        # )

        logger.info("PoE Dashboard application started successfully (basic mode)")

    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown"""
    logger.info("Shutting down PoE Dashboard application")
    app_state['shutdown_requested'] = True
    
    # Stop scheduler
    if app_state['scheduler']:
        await app_state['scheduler'].stop()
        
    # Close database connection
    if app_state['db_connection']:
        await app_state['db_connection'].close()
        
    logger.info("PoE Dashboard application shutdown complete")

# API Routes

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "PoE Dashboard API",
        "version": "1.0.0",
        "status": "running",
        "startup_time": app_state['startup_time'].isoformat() if app_state['startup_time'] else None
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    if not app_state['health_checker']:
        raise HTTPException(status_code=503, detail="Health checker not initialized")
        
    health_status = await app_state['health_checker'].get_health_status()
    
    if health_status['overall_status'] == 'healthy':
        return health_status
    else:
        return JSONResponse(
            status_code=503,
            content=health_status
        )

@app.get("/status")
async def get_status():
    """Get application status"""
    scheduler_status = app_state['scheduler'].get_status() if app_state['scheduler'] else None
    
    return {
        "application": {
            "startup_time": app_state['startup_time'].isoformat() if app_state['startup_time'] else None,
            "uptime_seconds": (datetime.now() - app_state['startup_time']).total_seconds() if app_state['startup_time'] else 0,
            "shutdown_requested": app_state['shutdown_requested']
        },
        "scheduler": scheduler_status,
        "settings": {
            "league": settings.api.default_league,
            "poll_interval_minutes": settings.scheduler.poll_interval_minutes,
            "environment": "development" if settings.is_development() else "production"
        }
    }

@app.get("/metrics")
async def get_metrics():
    """Get application metrics"""
    if not app_state['scheduler']:
        raise HTTPException(status_code=503, detail="Scheduler not initialized")
        
    # Get scheduler metrics
    scheduler_status = app_state['scheduler'].get_status()
    
    # Get API metrics
    api_metrics = {}
    if app_state['scheduler'].api_client:
        api_metrics = app_state['scheduler'].api_client.get_metrics_summary()
        
    # Get health metrics
    health_metrics = {}
    if app_state['health_checker']:
        health_metrics = await app_state['health_checker'].get_detailed_metrics()
        
    return {
        "scheduler": scheduler_status,
        "api": api_metrics,
        "health": health_metrics,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/scheduler/pause")
async def pause_scheduler():
    """Pause the data acquisition scheduler"""
    if not app_state['scheduler']:
        raise HTTPException(status_code=503, detail="Scheduler not initialized")
        
    await app_state['scheduler'].pause()
    return {"message": "Scheduler paused", "status": app_state['scheduler'].state.value}

@app.post("/scheduler/resume")
async def resume_scheduler():
    """Resume the data acquisition scheduler"""
    if not app_state['scheduler']:
        raise HTTPException(status_code=503, detail="Scheduler not initialized")
        
    await app_state['scheduler'].resume()
    return {"message": "Scheduler resumed", "status": app_state['scheduler'].state.value}

@app.get("/data/current-prices")
async def get_current_prices(league: Optional[str] = None, limit: int = 100):
    """Get current prices from materialized view"""
    if not app_state['db_connection']:
        raise HTTPException(status_code=503, detail="Database not available")
        
    try:
        query = """
            SELECT * FROM v_current_price 
            WHERE ($1 IS NULL OR league_name = $1)
            ORDER BY chaos_value DESC NULLS LAST
            LIMIT $2
        """
        
        results = await app_state['db_connection'].fetch_all(query, league, limit)
        return {
            "data": results,
            "count": len(results),
            "league": league or "all"
        }
        
    except Exception as e:
        logger.error(f"Failed to fetch current prices: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch current prices")

@app.get("/data/top-movers")
async def get_top_movers(league: Optional[str] = None, limit: int = 50):
    """Get top price movers from materialized view"""
    if not app_state['db_connection']:
        raise HTTPException(status_code=503, detail="Database not available")

    try:
        query = """
            SELECT * FROM v_top_movers
            WHERE ($1 IS NULL OR league_name = $1)
            ORDER BY ABS(momentum_7d) DESC
            LIMIT $2
        """

        results = await app_state['db_connection'].fetch_all(query, league, limit)
        return {
            "data": results,
            "count": len(results),
            "league": league or "all"
        }

    except Exception as e:
        logger.error(f"Failed to fetch top movers: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch top movers")

@app.get("/test/api")
async def test_api_connection(league: str = "Mercenaries"):
    """Test the PoE.ninja API connection"""
    try:
        from poe_ninja_api import PoeNinjaAPI, EndpointType

        async with PoeNinjaAPI(league=league) as api:
            # Test currency data
            currency_data = await api.get_currency_data()

            # Test item data
            weapon_data = await api.get_items_data(EndpointType.UNIQUE_WEAPON)

            # Get metrics
            metrics = api.get_metrics_summary()

            return {
                "status": "success",
                "league": league,
                "currency_items": len(currency_data.get('lines', [])) if currency_data else 0,
                "weapon_items": len(weapon_data.get('lines', [])) if weapon_data else 0,
                "api_metrics": metrics,
                "sample_currency": currency_data.get('lines', [{}])[0] if currency_data and currency_data.get('lines') else None,
                "sample_weapon": weapon_data.get('lines', [{}])[0] if weapon_data and weapon_data.get('lines') else None
            }

    except Exception as e:
        logger.error(f"API test failed: {e}")
        raise HTTPException(status_code=500, detail=f"API test failed: {str(e)}")

# Signal handlers for graceful shutdown
def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received signal {signum}, initiating graceful shutdown")
    app_state['shutdown_requested'] = True

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def main():
    """Main entry point"""
    settings = get_settings()
    
    logger.info(f"Starting PoE Dashboard on {settings.web.host}:{settings.web.port}")
    logger.info(f"Environment: {'development' if settings.is_development() else 'production'}")
    logger.info(f"Default league: {settings.api.default_league}")
    logger.info(f"Poll interval: {settings.scheduler.poll_interval_minutes} minutes")
    
    uvicorn.run(
        "main:app",
        host=settings.web.host,
        port=settings.web.port,
        reload=settings.is_development(),
        log_level=settings.logging.level.lower(),
        access_log=True
    )

if __name__ == "__main__":
    main()
