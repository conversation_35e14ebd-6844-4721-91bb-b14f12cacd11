import React from 'react';
import { TrendingUp, Clock, Shield, Copy, ExternalLink } from 'lucide-react';
import type { FlipOpportunity } from '../../types';
import { formatCurrency, formatPercentage, getRiskLevelColor } from '../../utils/tauri';

interface FlipOpportunityCardProps {
  opportunity: FlipOpportunity;
  rank: number;
}

const FlipOpportunityCard: React.FC<FlipOpportunityCardProps> = ({ opportunity, rank }) => {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const getRiskBadgeStyle = (risk: string) => {
    const baseStyle = "px-2 py-1 rounded-full text-xs font-medium";
    switch (risk.toLowerCase()) {
      case 'low':
        return `${baseStyle} bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300`;
      case 'medium':
        return `${baseStyle} bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300`;
      case 'high':
        return `${baseStyle} bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300`;
      default:
        return `${baseStyle} bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300`;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 dark:text-green-400';
    if (confidence >= 0.6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  return (
    <div className="card p-6 hover:shadow-lg transition-shadow" data-testid="flip-opportunity">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-poe-accent text-white rounded-full flex items-center justify-center text-sm font-bold">
            #{rank}
          </div>
          <div>
            <h3 className="font-semibold text-lg text-gray-900 dark:text-gray-100" data-testid="item-name">
              {opportunity.item_name}
            </h3>
            <div className={getRiskBadgeStyle(opportunity.risk_level)}>
              <Shield className="w-3 h-3 inline mr-1" />
              {opportunity.risk_level} Risk
            </div>
          </div>
        </div>
        
        <button
          onClick={() => copyToClipboard(opportunity.item_name)}
          className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          title="Copy item name"
        >
          <Copy className="w-4 h-4" />
        </button>
      </div>

      {/* Profit Highlight */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600 dark:text-gray-400">Expected Profit</p>
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              <span className="text-2xl font-bold profit-positive" data-testid="profit-pct">
                {formatPercentage(opportunity.profit_pct)}
              </span>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600 dark:text-gray-400">Profit Amount</p>
            <p className="text-xl font-bold profit-positive">
              +{formatCurrency(opportunity.profit_chaos, 'chaos')}
            </p>
          </div>
        </div>
      </div>

      {/* Price Details */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <p className="text-sm text-gray-600 dark:text-gray-400">Buy Price</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-gray-100" data-testid="buy-price">
            {formatCurrency(opportunity.buy_price, 'chaos')}
          </p>
        </div>
        
        <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <p className="text-sm text-gray-600 dark:text-gray-400">Sell Price</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            {formatCurrency(opportunity.sell_price, 'chaos')}
          </p>
        </div>
      </div>

      {/* Market Info */}
      <div className="grid grid-cols-3 gap-4 mb-4 text-center">
        <div>
          <p className="text-xs text-gray-600 dark:text-gray-400">Listings</p>
          <p className="font-medium text-gray-900 dark:text-gray-100">
            {opportunity.listing_count}
          </p>
        </div>
        
        <div>
          <p className="text-xs text-gray-600 dark:text-gray-400">Liquidity</p>
          <p className="font-medium text-gray-900 dark:text-gray-100">
            {(opportunity.liquidity_score * 100).toFixed(0)}%
          </p>
        </div>
        
        <div>
          <p className="text-xs text-gray-600 dark:text-gray-400">Confidence</p>
          <p className={`font-medium ${getConfidenceColor(opportunity.confidence)}`}>
            {(opportunity.confidence * 100).toFixed(0)}%
          </p>
        </div>
      </div>

      {/* Time Estimate */}
      <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-4">
        <div className="flex items-center">
          <Clock className="w-4 h-4 mr-1" />
          <span>Est. sell time: {opportunity.time_to_sell_estimate}</span>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-2">
        <button
          onClick={() => copyToClipboard(`${opportunity.item_name} - Buy: ${opportunity.buy_price}c, Sell: ${opportunity.sell_price}c, Profit: ${opportunity.profit_pct.toFixed(1)}%`)}
          className="flex-1 btn-secondary text-sm py-2"
        >
          <Copy className="w-4 h-4 mr-1" />
          Copy Details
        </button>
        
        <button
          className="flex-1 btn-primary text-sm py-2"
          title="Open in trade site (coming soon)"
          disabled
        >
          <ExternalLink className="w-4 h-4 mr-1" />
          Trade Site
        </button>
      </div>

      {/* Warning for high-risk items */}
      {opportunity.risk_level === 'High' && (
        <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded text-xs text-yellow-800 dark:text-yellow-300">
          ⚠️ High-risk investment: Price volatility may affect actual profits
        </div>
      )}
    </div>
  );
};

export default FlipOpportunityCard;
