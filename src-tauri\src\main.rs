// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::Arc;
use std::time::Instant;
use tauri::{Manager, State};
use tokio::sync::RwLock;

mod commands;
mod data;
mod models;
mod cache;
mod scheduler;
mod utils;
mod ai;

use commands::*;
use data::DatabaseManager;
use cache::CacheManager;
use scheduler::DataSyncScheduler;
use utils::config::AppConfig;
use ai::MarketAnalyzer;

// Application state
pub struct AppState {
    pub db_manager: Arc<DatabaseManager>,
    pub cache_manager: Arc<CacheManager>,
    pub sync_scheduler: Arc<RwLock<Option<DataSyncScheduler>>>,
    pub market_analyzer: Arc<RwLock<MarketAnalyzer>>,
    pub startup_time: Instant,
}

#[tokio::main]
async fn main() {
    let startup_time = Instant::now();
    
    // Initialize logging
    env_logger::init();
    log::info!("Starting PoE Profit AI...");
    
    // Load configuration
    let config = AppConfig::load().await.expect("Failed to load configuration");
    log::info!("Configuration loaded");
    
    // Initialize database
    let db_manager = Arc::new(
        DatabaseManager::new(&config.database_path)
            .await
            .expect("Failed to initialize database")
    );
    log::info!("Database initialized");
    
    // Initialize cache
    let cache_manager = Arc::new(CacheManager::new());
    log::info!("Cache manager initialized");

    // Initialize market analyzer
    let mut market_analyzer = MarketAnalyzer::new();
    if let Err(e) = market_analyzer.initialize(&db_manager).await {
        log::warn!("Failed to initialize market analyzer: {}", e);
    }
    let market_analyzer = Arc::new(RwLock::new(market_analyzer));
    log::info!("Market analyzer initialized");

    // Warm up cache with essential data
    warm_up_cache(&db_manager, &cache_manager).await;

    // Initialize sync scheduler
    let sync_scheduler = Arc::new(RwLock::new(None));
    
    let app_state = AppState {
        db_manager,
        cache_manager,
        sync_scheduler,
        market_analyzer,
        startup_time,
    };
    
    let startup_duration = startup_time.elapsed();
    log::info!("Application initialized in {:?}", startup_duration);
    
    tauri::Builder::default()
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            // Price commands
            quick_price_lookup,
            search_item_names,
            get_price_history,

            // Enhanced flip finder commands
            find_flip_opportunities,
            get_top_flip_opportunities,
            analyze_flip_opportunities_ai,

            // Portfolio commands
            get_portfolio_items,
            add_portfolio_item,
            remove_portfolio_item,
            get_portfolio_total_value,

            // Market commands
            get_market_summary,
            get_league_list,
            set_current_league,

            // AI/ML commands
            get_price_prediction,
            get_model_metrics,
            generate_data_quality_report,
            get_market_insights,
            retrain_models,

            // System commands
            get_app_info,
            get_performance_metrics,
            force_data_sync,
            get_sync_status,
        ])
        .setup(|app| {
            // Start background scheduler
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                if let Err(e) = start_background_scheduler(app_handle).await {
                    log::error!("Failed to start background scheduler: {}", e);
                }
            });
            
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

async fn warm_up_cache(
    db_manager: &Arc<DatabaseManager>,
    cache_manager: &Arc<CacheManager>,
) {
    log::info!("Warming up cache...");
    
    // Pre-load top traded items
    if let Ok(top_items) = db_manager.get_top_traded_items(100).await {
        for item in top_items {
            if let Ok(price) = db_manager.get_latest_price(item.id, "Standard").await {
                cache_manager.set_price_cache(&item.name, &price).await;
            }
        }
        log::info!("Cache warmed up with {} items", top_items.len());
    }
}

async fn start_background_scheduler(app_handle: tauri::AppHandle) -> Result<(), Box<dyn std::error::Error>> {
    let state: State<AppState> = app_handle.state();
    
    let scheduler = DataSyncScheduler::new(
        Arc::clone(&state.db_manager),
        app_handle.clone(),
    ).await?;
    
    scheduler.start().await?;
    
    // Store scheduler in app state
    let mut sync_scheduler = state.sync_scheduler.write().await;
    *sync_scheduler = Some(scheduler);
    
    log::info!("Background scheduler started");
    Ok(())
}
