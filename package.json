{"name": "poe-profit-ai", "private": true, "version": "1.0.0", "type": "module", "description": "PoE Profit AI - Stand-Alone Desktop Edition", "author": "PoE Profit AI Team", "license": "MIT", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "test": "vitest", "test:ui": "vitest --ui", "test:e2e": "playwright test", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write .", "type-check": "tsc --noEmit"}, "dependencies": {"@tauri-apps/api": "^2.0.0", "@tauri-apps/plugin-store": "^2.0.0", "@tauri-apps/plugin-updater": "^2.0.0", "@tauri-apps/plugin-window-state": "^2.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "zustand": "^4.4.7", "recharts": "^2.8.0", "lucide-react": "^0.294.0", "framer-motion": "^10.16.5", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lodash-es": "^4.17.21"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "@vitest/ui": "^0.34.6", "@playwright/test": "^1.40.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^22.1.0", "postcss": "^8.4.31", "prettier": "^3.0.3", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6"}}