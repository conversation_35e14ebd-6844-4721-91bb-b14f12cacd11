import React, { useState, useEffect } from 'react';
import { Brain, TrendingUp, Shield, AlertTriangle, CheckCircle } from 'lucide-react';
import { useAppStore } from '../../stores/appStore';
import { 
  getMarketInsights, 
  generateDataQualityReport, 
  getModelMetrics 
} from '../../utils/tauri';
import LoadingSpinner from '../UI/LoadingSpinner';

interface AIInsightsPanelProps {
  className?: string;
}

const AIInsightsPanel: React.FC<AIInsightsPanelProps> = ({ className = '' }) => {
  const [insights, setInsights] = useState<any>(null);
  const [qualityReport, setQualityReport] = useState<any>(null);
  const [modelMetrics, setModelMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'insights' | 'quality' | 'models'>('insights');
  const { currentLeague } = useAppStore();

  useEffect(() => {
    loadAIData();
  }, [currentLeague]);

  const loadAIData = async () => {
    try {
      setLoading(true);
      
      const [marketInsights, dataQuality, currencyMetrics] = await Promise.all([
        getMarketInsights(currentLeague),
        generateDataQualityReport(),
        getModelMetrics('Currency'),
      ]);
      
      setInsights(marketInsights);
      setQualityReport(dataQuality);
      setModelMetrics(currencyMetrics);
    } catch (error) {
      console.error('Failed to load AI data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getQualityColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600 dark:text-green-400';
    if (score >= 0.6) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getQualityIcon = (score: number) => {
    if (score >= 0.8) return <CheckCircle className="w-5 h-5 text-green-500" />;
    if (score >= 0.6) return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
    return <AlertTriangle className="w-5 h-5 text-red-500" />;
  };

  if (loading) {
    return (
      <div className={`card p-6 ${className}`}>
        <div className="text-center">
          <LoadingSpinner size="large" className="mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading AI insights...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`card p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold flex items-center text-gray-900 dark:text-gray-100">
          <Brain className="w-5 h-5 mr-2 text-purple-500" />
          AI Market Intelligence
        </h2>
        
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          {[
            { id: 'insights', label: 'Insights' },
            { id: 'quality', label: 'Quality' },
            { id: 'models', label: 'Models' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-gray-100 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Market Insights Tab */}
      {activeTab === 'insights' && insights && (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Opportunities</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {insights.total_opportunities}
              </p>
            </div>
            
            <div className="text-center p-3 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Avg Profit</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {insights.avg_profit_potential.toFixed(1)}%
              </p>
            </div>
            
            <div className="text-center p-3 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">High Confidence</p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {insights.high_confidence_opportunities}
              </p>
            </div>
            
            <div className="text-center p-3 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Volatility</p>
              <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {insights.market_volatility.toFixed(1)}%
              </p>
            </div>
          </div>

          {/* Top Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">
              Top Opportunity Categories
            </h3>
            <div className="space-y-2">
              {insights.top_categories.slice(0, 5).map((category: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div>
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      {category.category}
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
                      ({category.opportunity_count} items)
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold profit-positive">
                      {category.avg_profit.toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {(category.avg_confidence * 100).toFixed(0)}% confidence
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Risk Distribution */}
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">
              Risk Distribution
            </h3>
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <Shield className="w-6 h-6 text-green-500 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-400">Low Risk</p>
                <p className="text-xl font-bold text-green-600 dark:text-green-400">
                  {insights.risk_distribution.low_risk_count}
                </p>
              </div>
              
              <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <Shield className="w-6 h-6 text-yellow-500 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-400">Medium Risk</p>
                <p className="text-xl font-bold text-yellow-600 dark:text-yellow-400">
                  {insights.risk_distribution.medium_risk_count}
                </p>
              </div>
              
              <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <Shield className="w-6 h-6 text-red-500 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-400">High Risk</p>
                <p className="text-xl font-bold text-red-600 dark:text-red-400">
                  {insights.risk_distribution.high_risk_count}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Data Quality Tab */}
      {activeTab === 'quality' && qualityReport && (
        <div className="space-y-6">
          {/* Overall Quality Score */}
          <div className="text-center p-6 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg">
            <div className="flex items-center justify-center mb-3">
              {getQualityIcon(qualityReport.overall_score)}
              <span className="ml-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
                Data Quality Score
              </span>
            </div>
            <div className={`text-4xl font-bold ${getQualityColor(qualityReport.overall_score)}`}>
              {(qualityReport.overall_score * 100).toFixed(0)}%
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
              Based on {qualityReport.total_items_checked} items analyzed
            </p>
          </div>

          {/* Quality Metrics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Outliers</p>
              <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {qualityReport.outliers_detected}
              </p>
            </div>
            
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Missing Data</p>
              <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {qualityReport.missing_data_points}
              </p>
            </div>
            
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Freshness</p>
              <p className={`text-xl font-bold ${getQualityColor(qualityReport.data_freshness_score)}`}>
                {(qualityReport.data_freshness_score * 100).toFixed(0)}%
              </p>
            </div>
            
            <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-600 dark:text-gray-400">Issues</p>
              <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {qualityReport.quality_issues.length}
              </p>
            </div>
          </div>

          {/* Recent Issues */}
          {qualityReport.quality_issues.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-gray-100">
                Recent Quality Issues
              </h3>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {qualityReport.quality_issues.slice(0, 10).map((issue: any, index: number) => (
                  <div key={index} className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {issue.item_name}
                      </span>
                      <span className={`text-xs px-2 py-1 rounded ${
                        issue.severity === 'High' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                        issue.severity === 'Medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                        'bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-300'
                      }`}>
                        {issue.severity}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {issue.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Model Metrics Tab */}
      {activeTab === 'models' && (
        <div className="space-y-6">
          {modelMetrics ? (
            <>
              <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  {modelMetrics.model_name} Model
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Accuracy</p>
                    <p className="text-xl font-bold text-purple-600 dark:text-purple-400">
                      {(modelMetrics.accuracy * 100).toFixed(1)}%
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">MAE</p>
                    <p className="text-xl font-bold text-purple-600 dark:text-purple-400">
                      {modelMetrics.mean_absolute_error.toFixed(2)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">RMSE</p>
                    <p className="text-xl font-bold text-purple-600 dark:text-purple-400">
                      {modelMetrics.root_mean_square_error.toFixed(2)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Samples</p>
                    <p className="text-xl font-bold text-purple-600 dark:text-purple-400">
                      {modelMetrics.training_samples.toLocaleString()}
                    </p>
                  </div>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-4">
                  Last trained: {new Date(modelMetrics.last_trained).toLocaleString()}
                </p>
              </div>
            </>
          ) : (
            <div className="text-center p-6">
              <Brain className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                No Model Data Available
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Model metrics will appear here once training is complete.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AIInsightsPanel;
