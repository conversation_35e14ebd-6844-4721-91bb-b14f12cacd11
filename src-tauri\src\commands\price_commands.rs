use tauri::State;
use chrono::Utc;

use crate::{AppState, models::*};

#[tauri::command]
pub async fn quick_price_lookup(
    query: PriceQuery,
    state: State<'_, AppState>,
) -> Result<PriceResult, String> {
    let start_time = std::time::Instant::now();
    
    // Check cache first
    if let Some(cached) = state.cache_manager.get_price(&query.item_name).await {
        log::debug!("Cache hit for {}", query.item_name);
        return Ok(cached);
    }
    
    // Search for items
    let items = state.db_manager
        .search_items_fuzzy(&query.item_name, 5)
        .await
        .map_err(|e| format!("Search failed: {}", e))?;
    
    if items.is_empty() {
        return Err("Item not found".to_string());
    }
    
    // Get the best match
    let item = &items[0];
    let league = query.league.as_deref().unwrap_or("Standard");
    
    // Get latest price
    let price = state.db_manager
        .get_latest_price(item.id, league)
        .await
        .map_err(|e| format!("Price lookup failed: {}", e))?;
    
    // Calculate trend (simplified)
    let trend_7d = calculate_trend_7d(&state.db_manager, item.id, league).await.ok();
    
    let result = PriceResult {
        item_name: item.name.clone(),
        chaos_value: price.chaos_value,
        divine_value: price.divine_value,
        confidence: price.confidence_score,
        last_updated: price.snapshot_time.to_rfc3339(),
        trend_7d,
        liquidity: classify_liquidity(price.listing_count),
        listing_count: price.listing_count,
    };
    
    // Cache the result
    state.cache_manager.set_price_result(&query.item_name, &result).await;
    
    let duration = start_time.elapsed();
    log::info!("Price lookup for '{}' completed in {:?}", query.item_name, duration);
    
    Ok(result)
}

#[tauri::command]
pub async fn search_item_names(
    query: String,
    limit: usize,
    state: State<'_, AppState>,
) -> Result<Vec<String>, String> {
    // Check cache first
    if let Some(cached) = state.cache_manager.get_item_names(&query).await {
        return Ok(cached);
    }
    
    let names = state.db_manager
        .get_item_names(&query, limit)
        .await
        .map_err(|e| format!("Search failed: {}", e))?;
    
    // Cache the result
    state.cache_manager.set_item_names(&query, names.clone()).await;
    
    Ok(names)
}

#[tauri::command]
pub async fn get_price_history(
    item_name: String,
    league: String,
    days: i32,
    state: State<'_, AppState>,
) -> Result<Vec<Price>, String> {
    // This would require additional database queries
    // For now, return empty array
    Ok(vec![])
}

async fn calculate_trend_7d(
    db_manager: &crate::data::DatabaseManager,
    item_id: i64,
    league: &str,
) -> Result<f64, Box<dyn std::error::Error>> {
    // Simplified trend calculation
    // In a real implementation, you'd query historical prices
    Ok(5.0) // Placeholder: 5% increase
}

fn classify_liquidity(listing_count: i32) -> String {
    match listing_count {
        0..=5 => "Low".to_string(),
        6..=20 => "Medium".to_string(),
        21..=50 => "High".to_string(),
        _ => "Very High".to_string(),
    }
}
