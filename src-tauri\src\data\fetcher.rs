use reqwest::Client;
use std::time::Duration;
use tokio::time::sleep;
use anyhow::Result;
use governor::{Quota, RateLimiter, state::direct::NotKeyed, state::InMemoryState};
use std::num::NonZeroU32;

use crate::models::{PoeNinjaResponse, PoeNinjaItem};

#[derive(Clone)]
pub struct PoeNinjaClient {
    client: Client,
    base_url: String,
    rate_limiter: RateLimiter<NotKeyed, InMemoryState>,
}

#[derive(Debug, Clone)]
pub struct EndpointData {
    pub league: String,
    pub category: String,
    pub endpoint: String,
    pub data: PoeNinjaResponse,
}

impl PoeNinjaClient {
    pub fn new() -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .user_agent("PoE-Profit-AI/1.0")
            .build()
            .expect("Failed to create HTTP client");
        
        // Rate limiter: 45 requests per minute
        let quota = Quota::per_minute(NonZeroU32::new(45).unwrap());
        let rate_limiter = RateLimiter::direct(quota);
        
        Self {
            client,
            base_url: "https://poe.ninja/api/data".to_string(),
            rate_limiter,
        }
    }
    
    pub async fn fetch_all_data(&self, league: &str) -> Result<Vec<EndpointData>> {
        let endpoints = vec![
            ("currencyoverview", "Currency"),
            ("currencyoverview", "Fragment"),
            ("itemoverview", "DivinationCard"),
            ("itemoverview", "Artifact"),
            ("itemoverview", "Oil"),
            ("itemoverview", "Incubator"),
            ("itemoverview", "UniqueWeapon"),
            ("itemoverview", "UniqueArmour"),
            ("itemoverview", "UniqueAccessory"),
            ("itemoverview", "UniqueFlask"),
            ("itemoverview", "UniqueJewel"),
            ("itemoverview", "SkillGem"),
            ("itemoverview", "ClusterJewel"),
            ("itemoverview", "Map"),
            ("itemoverview", "BlightedMap"),
            ("itemoverview", "BlightRavagedMap"),
            ("itemoverview", "ScourgedMap"),
            ("itemoverview", "UniqueMap"),
            ("itemoverview", "DeliriumOrb"),
            ("itemoverview", "Invitation"),
            ("itemoverview", "Scarab"),
            ("itemoverview", "BaseType"),
            ("itemoverview", "Fossil"),
            ("itemoverview", "Resonator"),
            ("itemoverview", "HelmetEnchant"),
            ("itemoverview", "Beast"),
            ("itemoverview", "Essence"),
            ("itemoverview", "Vial"),
        ];
        
        let mut results = Vec::new();
        
        for (category, endpoint) in endpoints {
            match self.fetch_endpoint_data(league, category, endpoint).await {
                Ok(data) => {
                    log::info!("Successfully fetched {}: {} items", endpoint, data.data.lines.len());
                    results.push(data);
                }
                Err(e) => {
                    log::warn!("Failed to fetch {} {}: {}", category, endpoint, e);
                    // Continue with other endpoints
                }
            }
            
            // Small delay between requests
            sleep(Duration::from_millis(100)).await;
        }
        
        Ok(results)
    }
    
    pub async fn fetch_endpoint_data(
        &self,
        league: &str,
        category: &str,
        endpoint: &str,
    ) -> Result<EndpointData> {
        // Wait for rate limiter
        self.rate_limiter.until_ready().await;
        
        let url = format!(
            "{}/{}?league={}&type={}",
            self.base_url, category, league, endpoint
        );
        
        log::debug!("Fetching: {}", url);
        
        // Retry logic with exponential backoff
        let mut attempt = 0;
        let max_attempts = 3;
        
        loop {
            match self.fetch_with_timeout(&url).await {
                Ok(response) => {
                    if response.status().is_success() {
                        let data: PoeNinjaResponse = response.json().await?;
                        return Ok(EndpointData {
                            league: league.to_string(),
                            category: category.to_string(),
                            endpoint: endpoint.to_string(),
                            data,
                        });
                    } else if response.status().as_u16() == 429 {
                        // Rate limited - wait longer
                        let wait_time = Duration::from_secs(2_u64.pow(attempt + 2));
                        log::warn!("Rate limited, waiting {:?}", wait_time);
                        sleep(wait_time).await;
                    } else {
                        return Err(anyhow::anyhow!("HTTP error: {}", response.status()));
                    }
                }
                Err(e) if attempt < max_attempts - 1 => {
                    let wait_time = Duration::from_millis(500 * 2_u64.pow(attempt));
                    log::warn!("Request failed (attempt {}), retrying in {:?}: {}", 
                        attempt + 1, wait_time, e);
                    sleep(wait_time).await;
                }
                Err(e) => {
                    return Err(anyhow::anyhow!("All retry attempts failed: {}", e));
                }
            }
            
            attempt += 1;
            if attempt >= max_attempts {
                return Err(anyhow::anyhow!("Max retry attempts exceeded"));
            }
        }
    }
    
    async fn fetch_with_timeout(&self, url: &str) -> Result<reqwest::Response, reqwest::Error> {
        tokio::time::timeout(
            Duration::from_secs(30),
            self.client.get(url).send()
        ).await
        .map_err(|_| reqwest::Error::from(reqwest::ErrorKind::Timeout))?
    }
    
    pub async fn get_active_leagues(&self) -> Result<Vec<String>> {
        // For now, return hardcoded leagues
        // In a real implementation, you'd fetch this from the API
        Ok(vec![
            "Standard".to_string(),
            "Hardcore".to_string(),
            "Settlers".to_string(),
        ])
    }
}
