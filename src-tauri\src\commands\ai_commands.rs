use tauri::State;
use crate::{AppState, models::*};
use crate::ai::models::*;

#[tauri::command]
pub async fn analyze_flip_opportunities_ai(
    query: FlipFinderQuery,
    state: State<'_, AppState>,
) -> Result<Vec<FlipPrediction>, String> {
    let analyzer = state.market_analyzer.read().await;
    
    let opportunities = analyzer
        .analyze_flip_opportunities(
            &state.db_manager,
            query.budget_chaos,
            query.min_profit_pct,
            query.max_risk_level,
            &query.league,
        )
        .await
        .map_err(|e| format!("AI analysis failed: {}", e))?;
    
    log::info!("AI found {} flip opportunities", opportunities.len());
    Ok(opportunities)
}

#[tauri::command]
pub async fn get_price_prediction(
    item_id: i64,
    category: String,
    hours_ahead: u32,
    state: State<'_, AppState>,
) -> Result<PricePrediction, String> {
    let analyzer = state.market_analyzer.read().await;
    
    let prediction = analyzer
        .predictor
        .predict_price(&state.db_manager, item_id, &category, hours_ahead)
        .await
        .map_err(|e| format!("Price prediction failed: {}", e))?;
    
    Ok(prediction)
}

#[tauri::command]
pub async fn get_model_metrics(
    category: String,
    state: State<'_, AppState>,
) -> Result<Option<ModelMetrics>, String> {
    let analyzer = state.market_analyzer.read().await;
    
    let metrics = analyzer.predictor.get_model_metrics(&category).cloned();
    Ok(metrics)
}

#[tauri::command]
pub async fn generate_data_quality_report(
    state: State<'_, AppState>,
) -> Result<DataQualityReport, String> {
    let analyzer = state.market_analyzer.read().await;
    
    let report = analyzer
        .generate_data_quality_report(&state.db_manager)
        .await
        .map_err(|e| format!("Quality report generation failed: {}", e))?;
    
    Ok(report)
}

#[tauri::command]
pub async fn retrain_models(
    state: State<'_, AppState>,
) -> Result<String, String> {
    let mut analyzer = state.market_analyzer.write().await;
    
    analyzer
        .initialize(&state.db_manager)
        .await
        .map_err(|e| format!("Model retraining failed: {}", e))?;
    
    Ok("Models retrained successfully".to_string())
}

#[tauri::command]
pub async fn get_market_insights(
    league: String,
    state: State<'_, AppState>,
) -> Result<MarketInsights, String> {
    // Generate comprehensive market insights
    let analyzer = state.market_analyzer.read().await;
    
    // Get top opportunities
    let top_opportunities = analyzer
        .analyze_flip_opportunities(
            &state.db_manager,
            10000.0, // High budget for comprehensive analysis
            5.0,     // Low profit threshold
            RiskLevel::High, // All risk levels
            &league,
        )
        .await
        .map_err(|e| format!("Market analysis failed: {}", e))?;
    
    // Get data quality report
    let quality_report = analyzer
        .generate_data_quality_report(&state.db_manager)
        .await
        .map_err(|e| format!("Quality analysis failed: {}", e))?;
    
    // Calculate market statistics
    let total_opportunities = top_opportunities.len();
    let avg_profit = if !top_opportunities.is_empty() {
        top_opportunities.iter()
            .map(|o| o.profit_potential_24h)
            .sum::<f64>() / total_opportunities as f64
    } else {
        0.0
    };
    
    let high_confidence_count = top_opportunities.iter()
        .filter(|o| o.confidence_score > 0.8)
        .count();
    
    Ok(MarketInsights {
        league: league.clone(),
        total_opportunities,
        avg_profit_potential: avg_profit,
        high_confidence_opportunities: high_confidence_count,
        market_volatility: calculate_market_volatility(&top_opportunities),
        data_quality_score: quality_report.overall_score,
        top_categories: get_top_categories(&top_opportunities),
        risk_distribution: calculate_risk_distribution(&top_opportunities),
        generated_at: chrono::Utc::now(),
    })
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct MarketInsights {
    pub league: String,
    pub total_opportunities: usize,
    pub avg_profit_potential: f64,
    pub high_confidence_opportunities: usize,
    pub market_volatility: f64,
    pub data_quality_score: f64,
    pub top_categories: Vec<CategoryInsight>,
    pub risk_distribution: RiskDistribution,
    pub generated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct CategoryInsight {
    pub category: String,
    pub opportunity_count: usize,
    pub avg_profit: f64,
    pub avg_confidence: f64,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct RiskDistribution {
    pub low_risk_count: usize,
    pub medium_risk_count: usize,
    pub high_risk_count: usize,
}

fn calculate_market_volatility(opportunities: &[FlipPrediction]) -> f64 {
    if opportunities.is_empty() {
        return 0.0;
    }
    
    let profits: Vec<f64> = opportunities.iter()
        .map(|o| o.profit_potential_24h)
        .collect();
    
    let mean = profits.iter().sum::<f64>() / profits.len() as f64;
    let variance = profits.iter()
        .map(|p| (p - mean).powi(2))
        .sum::<f64>() / profits.len() as f64;
    
    variance.sqrt()
}

fn get_top_categories(opportunities: &[FlipPrediction]) -> Vec<CategoryInsight> {
    use std::collections::HashMap;
    
    let mut category_stats: HashMap<String, Vec<&FlipPrediction>> = HashMap::new();
    
    // Group by category (simplified - using first word of item name)
    for opp in opportunities {
        let category = opp.item_name.split_whitespace()
            .next()
            .unwrap_or("Unknown")
            .to_string();
        
        category_stats.entry(category).or_default().push(opp);
    }
    
    let mut insights: Vec<CategoryInsight> = category_stats.into_iter()
        .map(|(category, opps)| {
            let avg_profit = opps.iter()
                .map(|o| o.profit_potential_24h)
                .sum::<f64>() / opps.len() as f64;
            
            let avg_confidence = opps.iter()
                .map(|o| o.confidence_score)
                .sum::<f64>() / opps.len() as f64;
            
            CategoryInsight {
                category,
                opportunity_count: opps.len(),
                avg_profit,
                avg_confidence,
            }
        })
        .collect();
    
    insights.sort_by(|a, b| b.avg_profit.partial_cmp(&a.avg_profit).unwrap_or(std::cmp::Ordering::Equal));
    insights.truncate(10);
    
    insights
}

fn calculate_risk_distribution(opportunities: &[FlipPrediction]) -> RiskDistribution {
    let mut low_risk = 0;
    let mut medium_risk = 0;
    let mut high_risk = 0;
    
    for opp in opportunities {
        if opp.risk_score <= 0.3 {
            low_risk += 1;
        } else if opp.risk_score <= 0.6 {
            medium_risk += 1;
        } else {
            high_risk += 1;
        }
    }
    
    RiskDistribution {
        low_risk_count: low_risk,
        medium_risk_count: medium_risk,
        high_risk_count: high_risk,
    }
}
