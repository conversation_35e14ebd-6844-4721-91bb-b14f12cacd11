import React from 'react';
import { Refresh<PERSON>w, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { useAppStore } from '../../stores/appStore';
import { formatTimeAgo } from '../../utils/tauri';

const SyncStatusBar: React.FC = () => {
  const { syncStatus } = useAppStore();

  if (!syncStatus) return null;

  const getStatusIcon = () => {
    if (syncStatus.is_syncing) {
      return <RefreshCw className="w-4 h-4 animate-spin" />;
    }
    if (syncStatus.last_error) {
      return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
    if (syncStatus.last_sync) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    }
    return <Clock className="w-4 h-4 text-gray-500" />;
  };

  const getStatusText = () => {
    if (syncStatus.is_syncing) {
      return `Syncing... (${syncStatus.items_synced} items)`;
    }
    if (syncStatus.last_error) {
      return `Sync failed: ${syncStatus.last_error}`;
    }
    if (syncStatus.last_sync) {
      return `Last sync: ${formatTimeAgo(syncStatus.last_sync)} (${syncStatus.items_synced} items)`;
    }
    return 'No sync data available';
  };

  const getStatusColor = () => {
    if (syncStatus.is_syncing) {
      return 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300';
    }
    if (syncStatus.last_error) {
      return 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300';
    }
    if (syncStatus.last_sync) {
      return 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300';
    }
    return 'bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300';
  };

  return (
    <div className={`px-4 py-2 text-sm ${getStatusColor()}`} data-testid="sync-status">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span data-testid="sync-status-text">{getStatusText()}</span>
        </div>
        
        {syncStatus.next_sync_eta && !syncStatus.is_syncing && (
          <div className="text-xs opacity-75" data-testid="next-sync-time">
            Next sync: {formatTimeAgo(syncStatus.next_sync_eta)}
          </div>
        )}
      </div>
    </div>
  );
};

export default SyncStatusBar;
