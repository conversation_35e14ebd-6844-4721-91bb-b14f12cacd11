use anyhow::Result;
use chrono::{DateTime, Utc, Duration};
use statrs::statistics::{Statistics, OrderStatistics};

use crate::models::Price;
use crate::ai::models::*;

pub struct DataValidator {
    outlier_threshold: f64,
    max_price_change_pct: f64,
    min_confidence: f64,
    max_stale_hours: i64,
}

impl Default for DataValidator {
    fn default() -> Self {
        Self {
            outlier_threshold: 3.0,      // Z-score threshold
            max_price_change_pct: 1000.0, // 1000% max change
            min_confidence: 0.1,         // Minimum confidence score
            max_stale_hours: 4,          // Maximum data age in hours
        }
    }
}

impl DataValidator {
    pub fn new() -> Self {
        Self::default()
    }
    
    pub fn with_thresholds(
        outlier_threshold: f64,
        max_price_change_pct: f64,
        min_confidence: f64,
        max_stale_hours: i64,
    ) -> Self {
        Self {
            outlier_threshold,
            max_price_change_pct,
            min_confidence,
            max_stale_hours,
        }
    }
    
    /// Validate a single price point
    pub fn validate_price(&self, price: &Price, historical_prices: &[Price]) -> ValidationResult {
        let mut issues = Vec::new();
        let mut severity = IssueSeverity::Low;
        
        // Check for missing data
        if price.chaos_value.is_none() && price.divine_value.is_none() {
            issues.push("Missing price data".to_string());
            severity = IssueSeverity::High;
        }
        
        // Check confidence score
        if price.confidence_score < self.min_confidence {
            issues.push(format!("Low confidence score: {:.2}", price.confidence_score));
            if price.confidence_score < 0.05 {
                severity = IssueSeverity::High;
            } else {
                severity = severity.max(IssueSeverity::Medium);
            }
        }
        
        // Check data freshness
        let hours_old = Utc::now().signed_duration_since(price.snapshot_time).num_hours();
        if hours_old > self.max_stale_hours {
            issues.push(format!("Stale data: {} hours old", hours_old));
            severity = severity.max(IssueSeverity::Medium);
        }
        
        // Check for outliers if we have historical data
        if let Some(chaos_value) = price.chaos_value {
            if let Some(outlier_issue) = self.check_price_outlier(chaos_value, historical_prices) {
                issues.push(outlier_issue);
                severity = severity.max(IssueSeverity::Medium);
            }
        }
        
        // Check listing count reasonableness
        if price.listing_count < 0 {
            issues.push("Negative listing count".to_string());
            severity = IssueSeverity::High;
        } else if price.listing_count > 10000 {
            issues.push("Suspiciously high listing count".to_string());
            severity = severity.max(IssueSeverity::Medium);
        }
        
        ValidationResult {
            is_valid: issues.is_empty(),
            issues,
            severity,
            confidence_adjustment: self.calculate_confidence_adjustment(&issues, severity),
        }
    }
    
    fn check_price_outlier(&self, current_price: f64, historical_prices: &[Price]) -> Option<String> {
        if historical_prices.len() < 3 {
            return None; // Not enough data for outlier detection
        }
        
        let prices: Vec<f64> = historical_prices
            .iter()
            .filter_map(|p| p.chaos_value)
            .collect();
        
        if prices.len() < 3 {
            return None;
        }
        
        let mean = prices.iter().sum::<f64>() / prices.len() as f64;
        let variance = prices.iter()
            .map(|p| (p - mean).powi(2))
            .sum::<f64>() / prices.len() as f64;
        let std_dev = variance.sqrt();
        
        if std_dev == 0.0 {
            return None; // No variance in data
        }
        
        let z_score = (current_price - mean).abs() / std_dev;
        
        if z_score > self.outlier_threshold {
            Some(format!("Price outlier detected: {:.2}c (Z-score: {:.2})", current_price, z_score))
        } else {
            None
        }
    }
    
    fn calculate_confidence_adjustment(&self, issues: &[String], severity: IssueSeverity) -> f64 {
        let base_penalty = match severity {
            IssueSeverity::Low => 0.05,
            IssueSeverity::Medium => 0.15,
            IssueSeverity::High => 0.4,
            IssueSeverity::Critical => 0.8,
        };
        
        let issue_penalty = issues.len() as f64 * 0.05;
        
        -(base_penalty + issue_penalty).min(0.9) // Maximum 90% penalty
    }
    
    /// Validate a batch of prices and generate a comprehensive report
    pub fn validate_batch(&self, prices: &[Price], historical_data: &[Vec<Price>]) -> BatchValidationReport {
        let mut valid_count = 0;
        let mut quality_issues = Vec::new();
        let mut confidence_adjustments = Vec::new();
        
        for (i, price) in prices.iter().enumerate() {
            let historical = historical_data.get(i).map(|h| h.as_slice()).unwrap_or(&[]);
            let result = self.validate_price(price, historical);
            
            if result.is_valid {
                valid_count += 1;
            } else {
                for issue in &result.issues {
                    quality_issues.push(QualityIssue {
                        item_id: price.item_id,
                        item_name: format!("Item {}", price.item_id), // Would need item lookup
                        issue_type: self.classify_issue_type(issue),
                        severity: result.severity.clone(),
                        description: issue.clone(),
                        suggested_action: self.suggest_action(&result.severity),
                    });
                }
            }
            
            confidence_adjustments.push(result.confidence_adjustment);
        }
        
        let overall_quality = valid_count as f64 / prices.len() as f64;
        let avg_confidence_adjustment = confidence_adjustments.iter().sum::<f64>() / confidence_adjustments.len() as f64;
        
        BatchValidationReport {
            total_items: prices.len(),
            valid_items: valid_count,
            quality_score: overall_quality,
            avg_confidence_adjustment,
            quality_issues,
            generated_at: Utc::now(),
        }
    }
    
    fn classify_issue_type(&self, issue: &str) -> QualityIssueType {
        if issue.contains("outlier") {
            QualityIssueType::PriceOutlier
        } else if issue.contains("stale") || issue.contains("old") {
            QualityIssueType::StaleData
        } else if issue.contains("listing") {
            QualityIssueType::InconsistentListing
        } else if issue.contains("Missing") {
            QualityIssueType::MissingData
        } else {
            QualityIssueType::SuspiciousVolume
        }
    }
    
    fn suggest_action(&self, severity: &IssueSeverity) -> String {
        match severity {
            IssueSeverity::Low => "Monitor for trends".to_string(),
            IssueSeverity::Medium => "Review data source".to_string(),
            IssueSeverity::High => "Exclude from analysis".to_string(),
            IssueSeverity::Critical => "Immediate investigation required".to_string(),
        }
    }
    
    /// Apply data quality filters to a dataset
    pub fn filter_quality_data(&self, prices: &[Price]) -> Vec<Price> {
        prices
            .iter()
            .filter(|price| {
                // Basic quality filters
                price.chaos_value.is_some() &&
                price.confidence_score >= self.min_confidence &&
                price.listing_count >= 0 &&
                Utc::now().signed_duration_since(price.snapshot_time).num_hours() <= self.max_stale_hours
            })
            .cloned()
            .collect()
    }
}

#[derive(Debug, Clone)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub issues: Vec<String>,
    pub severity: IssueSeverity,
    pub confidence_adjustment: f64,
}

#[derive(Debug, Clone)]
pub struct BatchValidationReport {
    pub total_items: usize,
    pub valid_items: usize,
    pub quality_score: f64,
    pub avg_confidence_adjustment: f64,
    pub quality_issues: Vec<QualityIssue>,
    pub generated_at: DateTime<Utc>,
}

impl IssueSeverity {
    fn max(self, other: IssueSeverity) -> IssueSeverity {
        match (self, other) {
            (IssueSeverity::Critical, _) | (_, IssueSeverity::Critical) => IssueSeverity::Critical,
            (IssueSeverity::High, _) | (_, IssueSeverity::High) => IssueSeverity::High,
            (IssueSeverity::Medium, _) | (_, IssueSeverity::Medium) => IssueSeverity::Medium,
            _ => IssueSeverity::Low,
        }
    }
}
