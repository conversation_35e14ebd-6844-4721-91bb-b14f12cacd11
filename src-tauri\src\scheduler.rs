use tokio_cron_scheduler::{JobScheduler, Job};
use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};

use crate::data::{DatabaseManager, PoeNinjaClient, DataValidator};
use crate::models::SyncStatus;

pub struct DataSyncScheduler {
    scheduler: JobScheduler,
    db_manager: Arc<DatabaseManager>,
    api_client: PoeNinjaClient,
    data_validator: DataValidator,
    sync_state: Arc<RwLock<SyncStatus>>,
    app_handle: AppHandle,
}

impl DataSyncScheduler {
    pub async fn new(
        db_manager: Arc<DatabaseManager>,
        app_handle: AppHandle,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let scheduler = JobScheduler::new().await?;
        let api_client = PoeNinjaClient::new();
        let sync_state = Arc::new(RwLock::new(SyncStatus {
            is_syncing: false,
            last_sync: None,
            last_error: None,
            items_synced: 0,
            sync_duration_ms: 0,
            next_sync_eta: None,
        }));
        
        Ok(Self {
            scheduler,
            db_manager,
            api_client,
            data_validator: DataValidator::new(),
            sync_state,
            app_handle,
        })
    }
    
    pub async fn start(&self) -> Result<(), Box<dyn std::error::Error>> {
        let db_manager = Arc::clone(&self.db_manager);
        let api_client = self.api_client.clone();
        let sync_state = Arc::clone(&self.sync_state);
        let app_handle = self.app_handle.clone();
        
        // Schedule data sync every 31 minutes (as per blueprint)
        let sync_job = Job::new_async("0 */31 * * * *", move |_uuid, _l| {
            let db_manager = Arc::clone(&db_manager);
            let api_client = api_client.clone();
            let sync_state = Arc::clone(&sync_state);
            let app_handle = app_handle.clone();
            
            Box::pin(async move {
                if let Err(e) = perform_data_sync(&db_manager, &api_client, &sync_state, &app_handle).await {
                    log::error!("Data sync failed: {}", e);
                    
                    // Update sync state with error
                    let mut state = sync_state.write().await;
                    state.is_syncing = false;
                    state.last_error = Some(e.to_string());
                    
                    // Emit error event to frontend
                    let _ = app_handle.emit_all("sync-error", &e.to_string());
                }
            })
        })?;
        
        self.scheduler.add(sync_job).await?;
        self.scheduler.start().await?;
        
        // Perform initial sync
        let db_manager = Arc::clone(&self.db_manager);
        let api_client = self.api_client.clone();
        let sync_state = Arc::clone(&self.sync_state);
        let app_handle = self.app_handle.clone();
        
        tokio::spawn(async move {
            log::info!("Performing initial data sync...");
            if let Err(e) = perform_data_sync(&db_manager, &api_client, &sync_state, &app_handle).await {
                log::error!("Initial sync failed: {}", e);
            }
        });
        
        log::info!("Data sync scheduler started (31-minute intervals)");
        Ok(())
    }
    
    pub async fn get_sync_state(&self) -> SyncStatus {
        self.sync_state.read().await.clone()
    }
    
    pub async fn force_sync(&self) -> Result<(), Box<dyn std::error::Error>> {
        perform_data_sync(
            &self.db_manager,
            &self.api_client,
            &self.sync_state,
            &self.app_handle,
        ).await
    }
}

async fn perform_data_sync(
    db_manager: &Arc<DatabaseManager>,
    api_client: &PoeNinjaClient,
    sync_state: &Arc<RwLock<SyncStatus>>,
    app_handle: &AppHandle,
) -> Result<(), Box<dyn std::error::Error>> {
    let start_time = std::time::Instant::now();
    
    // Update sync state - start
    {
        let mut state = sync_state.write().await;
        state.is_syncing = true;
        state.last_error = None;
        state.items_synced = 0;
    }
    
    // Emit sync start event
    let _ = app_handle.emit_all("sync-started", ());
    
    log::info!("Starting data sync...");
    
    // Get active leagues
    let leagues = api_client.get_active_leagues().await?;
    let mut total_items_synced = 0;
    
    // Sync data for each league
    for league in &leagues {
        log::info!("Syncing data for league: {}", league);
        
        // Upsert league
        let league_id = db_manager.upsert_league(league, league).await?;
        
        // Fetch all endpoint data
        let endpoint_data = api_client.fetch_all_data(league).await?;
        
        // Process each endpoint
        for data in endpoint_data {
            let items_count = process_endpoint_data(db_manager, &data, league_id).await?;
            total_items_synced += items_count;
            
            // Update progress
            {
                let mut state = sync_state.write().await;
                state.items_synced = total_items_synced;
            }
            
            // Emit progress event
            let _ = app_handle.emit_all("sync-progress", total_items_synced);
        }
    }
    
    // Cleanup old data (keep 14 days)
    db_manager.cleanup_old_data(14).await?;
    
    let sync_duration = start_time.elapsed();
    
    // Update sync state - complete
    {
        let mut state = sync_state.write().await;
        state.last_sync = Some(Utc::now());
        state.is_syncing = false;
        state.items_synced = total_items_synced;
        state.sync_duration_ms = sync_duration.as_millis() as u64;
        state.next_sync_eta = Some(Utc::now() + chrono::Duration::minutes(31));
    }
    
    // Emit sync complete event
    let _ = app_handle.emit_all("sync-completed", total_items_synced);
    
    log::info!(
        "Data sync completed: {} items in {:?}",
        total_items_synced,
        sync_duration
    );
    
    Ok(())
}

async fn process_endpoint_data(
    db_manager: &Arc<DatabaseManager>,
    data: &crate::data::fetcher::EndpointData,
    league_id: i64,
) -> Result<u32, Box<dyn std::error::Error>> {
    let mut items_processed = 0;
    let mut items_validated = 0;
    let validator = DataValidator::new();

    for item in &data.data.lines {
        // Upsert item
        let item_id = db_manager.upsert_item(item, league_id, &data.endpoint).await?;

        // Validate data quality before insertion
        if let Some(chaos_value) = item.chaos_equivalent {
            // Get historical data for validation
            let historical_prices = db_manager.get_price_history(item_id, 7).await.unwrap_or_default();

            // Create a temporary price object for validation
            let temp_price = crate::models::Price {
                id: 0,
                item_id,
                league_id,
                chaos_value: Some(chaos_value),
                divine_value: item.divine_equivalent,
                listing_count: item.listing_count.unwrap_or(0),
                volatility: None,
                momentum_7d: None,
                liquidity_score: None,
                confidence_score: 1.0, // Default confidence
                is_outlier: false,
                snapshot_time: chrono::Utc::now(),
            };

            let validation_result = validator.validate_price(&temp_price, &historical_prices);

            if validation_result.is_valid || validation_result.confidence_adjustment > -0.5 {
                // Insert price data with adjusted confidence
                let adjusted_confidence = (1.0 + validation_result.confidence_adjustment).max(0.1);
                db_manager.insert_price_with_confidence(item_id, league_id, item, adjusted_confidence).await?;
                items_validated += 1;
            } else {
                log::warn!("Skipping low-quality data for item {}: {:?}", item_id, validation_result.issues);
            }
        } else {
            // Insert without chaos value (might be currency detail only)
            db_manager.insert_price(item_id, league_id, item).await?;
        }

        items_processed += 1;
    }

    log::debug!(
        "Processed {} items ({} validated) for {} {}",
        items_processed,
        items_validated,
        data.league,
        data.endpoint
    );

    Ok(items_processed)
}
