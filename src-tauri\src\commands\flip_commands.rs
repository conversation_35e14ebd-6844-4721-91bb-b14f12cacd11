use tauri::State;

use crate::{AppState, models::*};
use crate::commands::ai_commands::analyze_flip_opportunities_ai;

#[tauri::command]
pub async fn find_flip_opportunities(
    query: FlipFinderQuery,
    state: State<'_, AppState>,
) -> Result<Vec<FlipOpportunity>, String> {
    // Try AI-powered analysis first
    match analyze_flip_opportunities_ai(query.clone(), state.clone()).await {
        Ok(ai_predictions) => {
            // Convert AI predictions to FlipOpportunity format
            let opportunities: Vec<FlipOpportunity> = ai_predictions
                .into_iter()
                .map(|pred| FlipOpportunity {
                    item_name: pred.item_name,
                    buy_price: pred.current_price,
                    sell_price: pred.predicted_price_24h,
                    profit_chaos: pred.current_price * (pred.profit_potential_24h / 100.0),
                    profit_pct: pred.profit_potential_24h,
                    risk_level: if pred.risk_score <= 0.3 {
                        RiskLevel::Low
                    } else if pred.risk_score <= 0.6 {
                        RiskLevel::Medium
                    } else {
                        RiskLevel::High
                    },
                    confidence: pred.confidence_score,
                    liquidity_score: pred.liquidity_score,
                    time_to_sell_estimate: match pred.recommended_action {
                        crate::ai::models::FlipAction::Buy => "1-6 hours".to_string(),
                        _ => "6-24 hours".to_string(),
                    },
                    listing_count: 10, // Simplified
                })
                .collect();

            log::info!("Found {} AI-powered flip opportunities", opportunities.len());
            Ok(opportunities)
        }
        Err(_) => {
            // Fallback to traditional method
            log::warn!("AI analysis failed, falling back to traditional method");

            let opportunities = state.db_manager
                .get_arbitrage_opportunities(query.budget_chaos, query.min_profit_pct, &query.league)
                .await
                .map_err(|e| format!("Failed to find opportunities: {}", e))?;

            // Filter by risk level
            let filtered: Vec<FlipOpportunity> = opportunities
                .into_iter()
                .filter(|opp| opp.risk_level <= query.max_risk_level)
                .collect();

            log::info!("Found {} traditional flip opportunities", filtered.len());
            Ok(filtered)
        }
    }
}

#[tauri::command]
pub async fn get_top_flip_opportunities(
    league: String,
    limit: usize,
    state: State<'_, AppState>,
) -> Result<Vec<FlipOpportunity>, String> {
    let query = FlipFinderQuery {
        budget_chaos: 1000.0, // Default budget
        min_profit_pct: 5.0,  // Minimum 5% profit
        max_risk_level: RiskLevel::High,
        league,
    };
    
    let mut opportunities = find_flip_opportunities(query, state).await?;
    opportunities.truncate(limit);
    
    Ok(opportunities)
}
