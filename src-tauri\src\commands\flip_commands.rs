use tauri::State;

use crate::{AppState, models::*};

#[tauri::command]
pub async fn find_flip_opportunities(
    query: FlipFinderQuery,
    state: State<'_, AppState>,
) -> Result<Vec<FlipOpportunity>, String> {
    let opportunities = state.db_manager
        .get_arbitrage_opportunities(query.budget_chaos, query.min_profit_pct, &query.league)
        .await
        .map_err(|e| format!("Failed to find opportunities: {}", e))?;
    
    // Filter by risk level
    let filtered: Vec<FlipOpportunity> = opportunities
        .into_iter()
        .filter(|opp| opp.risk_level <= query.max_risk_level)
        .collect();
    
    log::info!("Found {} flip opportunities", filtered.len());
    
    Ok(filtered)
}

#[tauri::command]
pub async fn get_top_flip_opportunities(
    league: String,
    limit: usize,
    state: State<'_, AppState>,
) -> Result<Vec<FlipOpportunity>, String> {
    let query = FlipFinderQuery {
        budget_chaos: 1000.0, // Default budget
        min_profit_pct: 5.0,  // Minimum 5% profit
        max_risk_level: RiskLevel::High,
        league,
    };
    
    let mut opportunities = find_flip_opportunities(query, state).await?;
    opportunities.truncate(limit);
    
    Ok(opportunities)
}
