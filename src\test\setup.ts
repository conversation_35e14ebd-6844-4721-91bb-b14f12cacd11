import '@testing-library/jest-dom';

// Mock Tauri API
const mockInvoke = vi.fn();
const mockListen = vi.fn();

vi.mock('@tauri-apps/api/tauri', () => ({
  invoke: mockInvoke,
}));

vi.mock('@tauri-apps/api/event', () => ({
  listen: mockListen,
}));

// Global test utilities
global.mockInvoke = mockInvoke;
global.mockListen = mockListen;

// Reset mocks before each test
beforeEach(() => {
  mockInvoke.mockClear();
  mockListen.mockClear();
});
