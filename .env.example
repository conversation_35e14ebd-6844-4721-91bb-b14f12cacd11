# PoE Dashboard Environment Configuration
# Copy this file to .env and adjust values as needed

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/poe_dashboard
DB_MIN_CONNECTIONS=5
DB_MAX_CONNECTIONS=20

# API Configuration
POE_NINJA_BASE_URL=https://poe.ninja/api/data
POE_DEFAULT_LEAGUE=Mercenaries
API_MAX_CONCURRENT=6
API_TIMEOUT=30
API_MAX_REQ_PER_MIN=45
API_MAX_ENDPOINT_REQ_PER_MIN=5

# Scheduler Configuration (Blueprint Settings)
SCHEDULER_POLL_INTERVAL=31
SCHEDULER_OFFSET=1
SCHEDULER_JITTER=5
SCHEDULER_MAX_FAILURES=3
SCHEDULER_BACKOFF=60
SCHEDULER_AUTO_RESTART=true

# Data Validation Configuration
VALIDATION_MIN_LISTING_COUNT=10
VALIDATION_MIN_CONFIDENCE_COUNT=5
VALIDATION_MAX_OUTLIER_SIGMA=4.0
VALIDATION_CHAOS_MIN=0.5
VALIDATION_CHAOS_MAX=100000.0

# Web Server Configuration
WEB_HOST=0.0.0.0
WEB_PORT=8000
DEBUG=false
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE_PATH=./logs/poe_dashboard.log
LOG_MAX_FILE_SIZE_MB=100
LOG_BACKUP_COUNT=5

# Environment
ENVIRONMENT=development

# Redis Configuration (Optional)
REDIS_URL=redis://localhost:6379/0

# Monitoring Configuration (Optional)
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
