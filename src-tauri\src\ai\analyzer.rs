use std::collections::HashMap;
use anyhow::Result;
use chrono::{DateTime, Utc, Duration};
use statrs::statistics::{Statistics, OrderStatistics};

use crate::data::DatabaseManager;
use crate::models::{Item, Price, FlipOpportunity, RiskLevel};
use super::models::*;
use super::predictor::PricePredictor;

pub struct MarketAnalyzer {
    predictor: PricePredictor,
    quality_thresholds: QualityThresholds,
}

#[derive(Debug, Clone)]
struct QualityThresholds {
    outlier_z_score: f64,
    min_listings: i32,
    max_price_change_24h: f64,
    min_confidence: f64,
    stale_data_hours: i64,
}

impl Default for QualityThresholds {
    fn default() -> Self {
        Self {
            outlier_z_score: 3.0,
            min_listings: 3,
            max_price_change_24h: 500.0, // 500% change threshold
            min_confidence: 0.3,
            stale_data_hours: 2,
        }
    }
}

impl MarketAnalyzer {
    pub fn new() -> Self {
        Self {
            predictor: PricePredictor::new(),
            quality_thresholds: QualityThresholds::default(),
        }
    }
    
    pub async fn initialize(&mut self, db: &DatabaseManager) -> Result<()> {
        log::info!("Initializing market analyzer...");
        self.predictor.train_models(db).await?;
        log::info!("Market analyzer initialized successfully");
        Ok(())
    }
    
    pub async fn analyze_flip_opportunities(
        &self,
        db: &DatabaseManager,
        budget: f64,
        min_profit_pct: f64,
        max_risk: RiskLevel,
        league: &str,
    ) -> Result<Vec<FlipPrediction>> {
        log::info!("Analyzing flip opportunities for league: {}", league);
        
        // Get candidate items for analysis
        let candidates = self.get_flip_candidates(db, budget, league).await?;
        let mut flip_predictions = Vec::new();
        
        for item in candidates {
            match self.analyze_item_for_flip(db, &item, budget).await {
                Ok(Some(prediction)) => {
                    // Filter by criteria
                    if prediction.profit_potential_24h >= min_profit_pct 
                        && self.risk_level_matches(&prediction, &max_risk) {
                        flip_predictions.push(prediction);
                    }
                }
                Ok(None) => continue,
                Err(e) => {
                    log::warn!("Failed to analyze item {}: {}", item.name, e);
                    continue;
                }
            }
        }
        
        // Sort by profit potential
        flip_predictions.sort_by(|a, b| {
            b.profit_potential_24h.partial_cmp(&a.profit_potential_24h)
                .unwrap_or(std::cmp::Ordering::Equal)
        });
        
        // Limit results
        flip_predictions.truncate(50);
        
        log::info!("Found {} flip opportunities", flip_predictions.len());
        Ok(flip_predictions)
    }
    
    async fn get_flip_candidates(&self, db: &DatabaseManager, budget: f64, league: &str) -> Result<Vec<Item>> {
        // Get items that are within budget and have sufficient trading activity
        let candidates = sqlx::query_as::<_, Item>(
            r#"
            SELECT DISTINCT i.* FROM items i
            JOIN prices p ON i.id = p.item_id
            JOIN leagues l ON p.league_id = l.id
            WHERE l.name = ?
            AND p.chaos_value <= ?
            AND p.chaos_value > 0.1
            AND p.listing_count >= 5
            AND p.snapshot_time >= datetime('now', '-2 hours')
            ORDER BY p.listing_count DESC
            LIMIT 200
            "#
        )
        .bind(league)
        .bind(budget)
        .fetch_all(db.get_pool())
        .await?;
        
        Ok(candidates)
    }
    
    async fn analyze_item_for_flip(&self, db: &DatabaseManager, item: &Item, budget: f64) -> Result<Option<FlipPrediction>> {
        // Get current price data
        let current_price_data = db.get_latest_price(item.id, "Standard").await?;
        let current_price = current_price_data.chaos_value.unwrap_or(0.0);
        
        if current_price <= 0.0 || current_price > budget {
            return Ok(None);
        }
        
        // Get price predictions
        let pred_1h = self.predictor.predict_price(db, item.id, &item.category, 1).await?;
        let pred_6h = self.predictor.predict_price(db, item.id, &item.category, 6).await?;
        let pred_24h = self.predictor.predict_price(db, item.id, &item.category, 24).await?;
        
        // Calculate profit potentials
        let profit_1h = ((pred_1h.predicted_price - current_price) / current_price) * 100.0;
        let profit_6h = ((pred_6h.predicted_price - current_price) / current_price) * 100.0;
        let profit_24h = ((pred_24h.predicted_price - current_price) / current_price) * 100.0;
        
        // Calculate risk and liquidity scores
        let risk_score = self.calculate_risk_score(db, item, &current_price_data).await?;
        let liquidity_score = self.calculate_liquidity_score(&current_price_data);
        
        // Determine recommended action
        let recommended_action = self.determine_flip_action(profit_24h, risk_score, liquidity_score);
        
        // Generate reasoning
        let reasoning = self.generate_flip_reasoning(profit_24h, risk_score, liquidity_score, &recommended_action);
        
        // Average confidence from predictions
        let confidence_score = (pred_1h.confidence + pred_6h.confidence + pred_24h.confidence) / 3.0;
        
        Ok(Some(FlipPrediction {
            item_id: item.id,
            item_name: item.name.clone(),
            current_price,
            predicted_price_1h: pred_1h.predicted_price,
            predicted_price_6h: pred_6h.predicted_price,
            predicted_price_24h: pred_24h.predicted_price,
            profit_potential_1h: profit_1h,
            profit_potential_6h: profit_6h,
            profit_potential_24h: profit_24h,
            confidence_score,
            risk_score,
            liquidity_score,
            recommended_action,
            reasoning,
        }))
    }
    
    async fn calculate_risk_score(&self, db: &DatabaseManager, item: &Item, current_price: &Price) -> Result<f64> {
        // Get price history for volatility calculation
        let price_history = db.get_price_history(item.id, 7).await?;
        
        if price_history.len() < 3 {
            return Ok(0.8); // High risk for insufficient data
        }
        
        let prices: Vec<f64> = price_history.iter()
            .filter_map(|p| p.chaos_value)
            .collect();
        
        if prices.is_empty() {
            return Ok(0.8);
        }
        
        // Calculate volatility (coefficient of variation)
        let mean = prices.iter().sum::<f64>() / prices.len() as f64;
        let variance = prices.iter()
            .map(|p| (p - mean).powi(2))
            .sum::<f64>() / prices.len() as f64;
        let std_dev = variance.sqrt();
        let cv = if mean > 0.0 { std_dev / mean } else { 1.0 };
        
        // Risk factors
        let volatility_risk = cv.min(1.0);
        let liquidity_risk = if current_price.listing_count < 10 { 0.3 } else { 0.0 };
        let confidence_risk = 1.0 - current_price.confidence_score;
        
        // Combine risk factors (0 = low risk, 1 = high risk)
        let total_risk = (volatility_risk + liquidity_risk + confidence_risk) / 3.0;
        Ok(total_risk.min(1.0))
    }
    
    fn calculate_liquidity_score(&self, price_data: &Price) -> f64 {
        // Simple liquidity score based on listing count
        let listing_count = price_data.listing_count as f64;
        
        // Normalize to 0-1 scale
        if listing_count >= 50.0 {
            1.0
        } else if listing_count >= 20.0 {
            0.8
        } else if listing_count >= 10.0 {
            0.6
        } else if listing_count >= 5.0 {
            0.4
        } else {
            0.2
        }
    }
    
    fn determine_flip_action(&self, profit_24h: f64, risk_score: f64, liquidity_score: f64) -> FlipAction {
        if profit_24h >= 15.0 && risk_score <= 0.4 && liquidity_score >= 0.6 {
            FlipAction::Buy
        } else if profit_24h >= 8.0 && risk_score <= 0.6 && liquidity_score >= 0.4 {
            FlipAction::Buy
        } else if profit_24h <= -10.0 {
            FlipAction::Sell
        } else if risk_score >= 0.7 || liquidity_score <= 0.3 {
            FlipAction::Avoid
        } else {
            FlipAction::Hold
        }
    }
    
    fn generate_flip_reasoning(&self, profit_24h: f64, risk_score: f64, liquidity_score: f64, action: &FlipAction) -> String {
        match action {
            FlipAction::Buy => {
                format!("Strong buy signal: {:.1}% profit potential with {:.0}% confidence. Risk level: {}",
                    profit_24h,
                    (1.0 - risk_score) * 100.0,
                    if risk_score <= 0.3 { "Low" } else if risk_score <= 0.6 { "Medium" } else { "High" }
                )
            }
            FlipAction::Sell => {
                format!("Sell recommendation: Predicted {:.1}% decline. Consider liquidating position.",
                    profit_24h.abs())
            }
            FlipAction::Hold => {
                format!("Hold position: Moderate {:.1}% potential with balanced risk profile.",
                    profit_24h)
            }
            FlipAction::Avoid => {
                format!("Avoid trade: High risk ({:.0}%) or low liquidity ({:.0}%) detected.",
                    risk_score * 100.0,
                    liquidity_score * 100.0)
            }
        }
    }
    
    fn risk_level_matches(&self, prediction: &FlipPrediction, max_risk: &RiskLevel) -> bool {
        let item_risk = if prediction.risk_score <= 0.3 {
            RiskLevel::Low
        } else if prediction.risk_score <= 0.6 {
            RiskLevel::Medium
        } else {
            RiskLevel::High
        };
        
        match max_risk {
            RiskLevel::Low => matches!(item_risk, RiskLevel::Low),
            RiskLevel::Medium => matches!(item_risk, RiskLevel::Low | RiskLevel::Medium),
            RiskLevel::High => true, // Accept all risk levels
        }
    }
    
    pub async fn generate_data_quality_report(&self, db: &DatabaseManager) -> Result<DataQualityReport> {
        log::info!("Generating data quality report...");
        
        let mut quality_issues = Vec::new();
        let mut total_items = 0;
        let mut outliers_detected = 0;
        let mut missing_data_points = 0;
        
        // Get all active items
        let items = sqlx::query_as::<_, Item>(
            "SELECT * FROM items LIMIT 1000"
        )
        .fetch_all(db.get_pool())
        .await?;
        
        total_items = items.len();
        
        for item in items {
            // Check for outliers
            let outliers = db.detect_price_outliers(item.id, self.quality_thresholds.outlier_z_score).await?;
            outliers_detected += outliers.len();
            
            for outlier in outliers {
                quality_issues.push(QualityIssue {
                    item_id: item.id,
                    item_name: item.name.clone(),
                    issue_type: QualityIssueType::PriceOutlier,
                    severity: IssueSeverity::Medium,
                    description: format!("Price outlier detected: {:.2}c", outlier.chaos_value.unwrap_or(0.0)),
                    suggested_action: "Review price data for accuracy".to_string(),
                });
            }
            
            // Check for stale data
            if let Ok(latest_price) = db.get_latest_price(item.id, "Standard").await {
                let hours_old = Utc::now().signed_duration_since(latest_price.snapshot_time).num_hours();
                if hours_old > self.quality_thresholds.stale_data_hours {
                    quality_issues.push(QualityIssue {
                        item_id: item.id,
                        item_name: item.name.clone(),
                        issue_type: QualityIssueType::StaleData,
                        severity: if hours_old > 24 { IssueSeverity::High } else { IssueSeverity::Low },
                        description: format!("Data is {} hours old", hours_old),
                        suggested_action: "Force data refresh".to_string(),
                    });
                }
            } else {
                missing_data_points += 1;
                quality_issues.push(QualityIssue {
                    item_id: item.id,
                    item_name: item.name.clone(),
                    issue_type: QualityIssueType::MissingData,
                    severity: IssueSeverity::Medium,
                    description: "No price data available".to_string(),
                    suggested_action: "Check data source connectivity".to_string(),
                });
            }
        }
        
        // Calculate overall quality score
        let issue_penalty = quality_issues.len() as f64 / total_items as f64;
        let overall_score = (1.0 - issue_penalty).max(0.0).min(1.0);
        
        // Calculate data freshness score
        let stale_issues = quality_issues.iter()
            .filter(|issue| matches!(issue.issue_type, QualityIssueType::StaleData))
            .count();
        let data_freshness_score = 1.0 - (stale_issues as f64 / total_items as f64);
        
        Ok(DataQualityReport {
            total_items_checked: total_items,
            outliers_detected,
            missing_data_points,
            data_freshness_score,
            confidence_distribution: vec![0.8, 0.9, 0.95], // Simplified
            quality_issues,
            overall_score,
            generated_at: Utc::now(),
        })
    }
}
