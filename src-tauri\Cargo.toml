[package]
name = "poe-profit-ai"
version = "1.0.0"
description = "PoE Profit AI - Stand-Alone Desktop Edition"
authors = ["PoE Profit AI Team"]
license = "MIT"
repository = "https://github.com/yourusername/poe-profit-ai"
edition = "2021"

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
# Core Tauri
tauri = { version = "2.0", features = ["protocol-asset", "shell-open"] }
tauri-plugin-store = "2.0"
tauri-plugin-window-state = "2.0"
tauri-plugin-updater = "2.0"

# Async runtime
tokio = { version = "1.0", features = ["full"] }
tokio-cron-scheduler = "0.10"
futures = "0.3"

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }

# HTTP client
reqwest = { version = "0.11", features = ["json", "rustls-tls"] }
url = "2.4"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Utilities
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
anyhow = "1.0"
thiserror = "1.0"

# Logging
log = "0.4"
env_logger = "0.10"

# System monitoring
sysinfo = "0.29"

# Rate limiting
governor = "0.6"

# Caching
moka = { version = "0.12", features = ["future"] }

# Fuzzy search
fuzzy-matcher = "0.3"

# AI/ML dependencies
linfa = "0.7"
linfa-linear = "0.7"
linfa-preprocessing = "0.7"
ndarray = "0.15"
smartcore = "0.3"

# Statistical analysis
statrs = "0.16"

# Additional dependencies
dirs = "5.0"

[dev-dependencies]
tempfile = "3.8"
tokio-test = "0.4"

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]

# Optimize for release builds
[profile.release]
panic = "abort"
codegen-units = 1
lto = true
opt-level = "s"  # Optimize for size
strip = true     # Remove debug symbols
